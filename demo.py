#!/usr/bin/env python3
"""
数据字段检索器演示脚本
展示各种搜索功能和用例
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = 'http://localhost:5000'

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_result_summary(result):
    """打印搜索结果摘要"""
    if result.get('success'):
        count = result.get('count', 0)
        print(f"✅ 搜索成功！找到 {count} 个结果")
        
        # 显示前几个结果
        results = result.get('results', [])
        for i, item in enumerate(results[:3], 1):
            category = item.get('category.name') or (item.get('category', {}).get('name') if isinstance(item.get('category'), dict) else 'N/A')
            coverage = item.get('coverage', 0)
            coverage_pct = f"{coverage*100:.1f}%" if coverage else "N/A"
            
            print(f"  {i}. {item.get('id', 'N/A')}")
            print(f"     描述: {item.get('description', 'N/A')[:80]}...")
            print(f"     类别: {category} | 类型: {item.get('type', 'N/A')} | 覆盖率: {coverage_pct}")
    else:
        print(f"❌ 搜索失败: {result.get('error', '未知错误')}")

def test_local_searches():
    """测试本地搜索功能"""
    print_header("本地数据搜索演示")
    
    test_cases = [
        {
            "name": "搜索所有包含'price'的字段",
            "params": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "price",
                "category": "",
                "type": "",
                "limit": 5,
                "use_api": False
            }
        },
        {
            "name": "搜索analyst类别的所有MATRIX数据",
            "params": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "",
                "category": "analyst",
                "type": "MATRIX",
                "limit": 5,
                "use_api": False
            }
        },
        {
            "name": "搜索fundamental类别中包含'revenue'的字段",
            "params": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "revenue",
                "category": "fundamental",
                "type": "",
                "limit": 5,
                "use_api": False
            }
        },
        {
            "name": "搜索model类别的所有数据类型",
            "params": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "",
                "category": "model",
                "type": "",
                "limit": 8,
                "use_api": False
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 50)
        
        try:
            response = requests.post(f'{BASE_URL}/search', json=test_case['params'], timeout=10)
            if response.status_code == 200:
                result = response.json()
                print_result_summary(result)
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(0.5)

def test_different_regions():
    """测试不同区域的数据"""
    print_header("不同区域数据搜索演示")
    
    regions = ['USA', 'EUR', 'CHN']
    
    for region in regions:
        print(f"\n搜索 {region} 区域的价格数据:")
        print("-" * 30)
        
        params = {
            "region": region,
            "delay": 1,
            "universe": "TOP3000" if region == "USA" else ("TOP2500" if region == "EUR" else "TOP2000U"),
            "search": "price",
            "category": "pv",
            "type": "",
            "limit": 3,
            "use_api": False
        }
        
        try:
            response = requests.post(f'{BASE_URL}/search', json=params, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print_result_summary(result)
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求错误: {e}")

def test_data_types():
    """测试不同数据类型"""
    print_header("不同数据类型搜索演示")
    
    data_types = ['MATRIX', 'VECTOR', 'GROUP', 'UNIVERSE']
    
    for data_type in data_types:
        print(f"\n搜索 {data_type} 类型的数据:")
        print("-" * 30)
        
        params = {
            "region": "USA",
            "delay": 1,
            "universe": "TOP3000",
            "search": "",
            "category": "",
            "type": data_type,
            "limit": 3,
            "use_api": False
        }
        
        try:
            response = requests.post(f'{BASE_URL}/search', json=params, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print_result_summary(result)
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求错误: {e}")

def test_api_connection():
    """测试API连接"""
    print_header("API连接测试")
    
    try:
        response = requests.get(f'{BASE_URL}/test_connection', timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API连接成功！")
                print(f"   用户ID: {result.get('user_id', 'N/A')}")
                print(f"   状态码: {result.get('status_code', 'N/A')}")
                return True
            else:
                print(f"❌ API连接失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 连接错误: {e}")
    
    return False

def show_usage_examples():
    """显示使用示例"""
    print_header("使用示例和建议")
    
    examples = [
        {
            "场景": "寻找盈利相关指标",
            "搜索词": "earnings, profit, income",
            "建议类别": "analyst, fundamental",
            "建议类型": "MATRIX"
        },
        {
            "场景": "寻找价格和成交量数据",
            "搜索词": "price, volume, vwap",
            "建议类别": "pv",
            "建议类型": "MATRIX"
        },
        {
            "场景": "寻找风险评估指标",
            "搜索词": "risk, volatility, beta",
            "建议类别": "risk, model",
            "建议类型": "MATRIX"
        },
        {
            "场景": "寻找行业分类数据",
            "搜索词": "sector, industry, group",
            "建议类别": "pv",
            "建议类型": "GROUP"
        },
        {
            "场景": "寻找股票池定义",
            "搜索词": "top, universe",
            "建议类别": "pv",
            "建议类型": "UNIVERSE"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['场景']}")
        print(f"   推荐搜索词: {example['搜索词']}")
        print(f"   推荐类别: {example['建议类别']}")
        print(f"   推荐类型: {example['建议类型']}")

def main():
    """主函数"""
    print("数据字段检索器 - 完整功能演示")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查服务状态
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行，请先启动 app.py")
            return
    except:
        print("❌ 无法连接到服务，请确保 app.py 正在运行")
        return
    
    print("✅ 服务运行正常，开始演示...")
    
    # 执行各种测试
    test_local_searches()
    test_different_regions()
    test_data_types()
    
    # 测试API连接
    api_available = test_api_connection()
    
    # 显示使用建议
    show_usage_examples()
    
    print_header("演示完成")
    print("🎉 所有功能演示完成！")
    print("\n📝 使用说明:")
    print("1. 在浏览器中访问 http://localhost:5000")
    print("2. 使用左侧面板设置搜索条件")
    print("3. 点击'搜索'按钮查看结果")
    print("4. 可以选择使用本地数据或API搜索")
    if api_available:
        print("5. API连接正常，可以使用实时搜索功能")
    else:
        print("5. API连接异常，建议使用本地搜索功能")

if __name__ == "__main__":
    main()
