#!/usr/bin/env python3
"""
测试公式编辑器功能
"""

import requests
import time

def test_formula_editor_workflow():
    """测试完整的公式编辑器工作流程"""
    print("📝 测试公式编辑器功能...")
    
    # 1. 首先进行搜索获取因子数据
    print("\n1️⃣ 进行搜索获取因子数据...")
    
    search_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 10,
        'use_api': True
    }
    
    try:
        response = requests.post('http://localhost:5000/search', json=search_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                factors = result.get('results', [])
                print(f"✅ 搜索成功！获得 {len(factors)} 个因子")
                
                # 显示前5个因子
                print("🔍 前5个因子:")
                for i, factor in enumerate(factors[:5], 1):
                    print(f"   {i}. {factor.get('id', 'N/A')} - {factor.get('description', 'N/A')[:50]}...")
                
                return factors
            else:
                print(f"❌ 搜索失败: {result.get('error')}")
                return []
        elif response.status_code == 401:
            print("❌ 需要先在浏览器中登录API账户")
            return []
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 搜索请求错误: {e}")
        return []

def simulate_formula_generation(factors):
    """模拟公式生成过程"""
    print("\n2️⃣ 模拟公式生成...")
    
    if not factors:
        print("❌ 没有因子数据，无法生成公式")
        return
    
    # 测试用例
    test_cases = [
        {
            'name': '简单比率公式',
            'template': '<price_factor/> / <volume_factor/>',
            'top_n': 3,
            'mapping': 'sequential'
        },
        {
            'name': '复杂数学公式',
            'template': 'log(<market_cap/>) * sqrt(<pe_ratio/>)',
            'top_n': 4,
            'mapping': 'sequential'
        },
        {
            'name': '多因子组合',
            'template': '(<factor1/> + <factor2/> + <factor3/>) / 3',
            'top_n': 5,
            'mapping': 'sequential'
        },
        {
            'name': '所有组合模式',
            'template': '<factor_a/> - <factor_b/>',
            'top_n': 3,
            'mapping': 'all_combinations'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        print(f"模板: {test_case['template']}")
        print(f"使用前{test_case['top_n']}个因子，{test_case['mapping']}模式")
        
        # 提取占位符
        import re
        placeholders = re.findall(r'<([^/>]+)/>', test_case['template'])
        print(f"占位符: {placeholders}")
        
        # 选择因子
        selected_factors = factors[:test_case['top_n']]
        print(f"选中因子: {[f.get('id', 'N/A') for f in selected_factors]}")
        
        # 生成公式
        formulas = generate_formulas_python(
            test_case['template'], 
            placeholders, 
            selected_factors, 
            test_case['mapping']
        )
        
        print(f"生成公式数量: {len(formulas)}")
        print("前3个公式:")
        for j, formula in enumerate(formulas[:3], 1):
            print(f"   {j}. {formula}")
        
        if len(formulas) > 3:
            print(f"   ... (还有{len(formulas) - 3}个)")

def generate_formulas_python(template, placeholders, factors, mapping_type):
    """Python版本的公式生成函数（模拟JavaScript逻辑）"""
    formulas = []
    
    if mapping_type == 'sequential':
        # 顺序填充
        for i in range(len(factors)):
            formula = template
            for j, placeholder in enumerate(placeholders):
                factor_index = (i + j) % len(factors)
                factor_id = factors[factor_index].get('id', 'N/A')
                formula = formula.replace(f'<{placeholder}/>', factor_id)
            formulas.append(formula)
    
    elif mapping_type == 'all_combinations':
        # 所有组合
        if len(placeholders) == 1:
            # 单个占位符
            for factor in factors:
                formula = template.replace(f'<{placeholders[0]}/>', factor.get('id', 'N/A'))
                formulas.append(formula)
        
        elif len(placeholders) == 2:
            # 两个占位符
            for factor1 in factors:
                for factor2 in factors:
                    if factor1.get('id') != factor2.get('id'):
                        formula = template
                        formula = formula.replace(f'<{placeholders[0]}/>', factor1.get('id', 'N/A'))
                        formula = formula.replace(f'<{placeholders[1]}/>', factor2.get('id', 'N/A'))
                        formulas.append(formula)
        
        else:
            # 多个占位符（简化版本，限制组合数量）
            import itertools
            max_combinations = 20
            count = 0
            
            for combination in itertools.permutations(factors, min(len(placeholders), len(factors))):
                if count >= max_combinations:
                    break
                
                formula = template
                for placeholder, factor in zip(placeholders, combination):
                    formula = formula.replace(f'<{placeholder}/>', factor.get('id', 'N/A'))
                formulas.append(formula)
                count += 1
    
    return formulas

def test_formula_export_format():
    """测试公式导出格式"""
    print("\n3️⃣ 测试公式导出格式...")
    
    sample_formulas = [
        'CLOSE_PRICE / VOLUME',
        'log(MARKET_CAP) * sqrt(PE_RATIO)',
        '(FACTOR1 + FACTOR2 + FACTOR3) / 3',
        'HIGH_PRICE - LOW_PRICE',
        'OPEN_PRICE / CLOSE_PRICE'
    ]
    
    # 模拟逗号分隔格式
    comma_separated = ',\n'.join(sample_formulas)
    
    print("生成的公式（逗号分隔格式）:")
    print("-" * 40)
    print(comma_separated)
    print("-" * 40)
    
    print(f"✅ 格式正确，共{len(sample_formulas)}个公式")
    print("💡 这种格式可以直接复制到其他系统中使用")

def main():
    print("=" * 60)
    print("📝 公式编辑器功能测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常")
    print("💡 请确保已在浏览器中登录API账户\n")
    
    # 执行测试
    factors = test_formula_editor_workflow()
    
    if factors:
        simulate_formula_generation(factors)
        test_formula_export_format()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if factors:
        print("🎉 公式编辑器功能测试完成！")
        print("\n✅ 功能特性:")
        print("   - 📝 支持占位符模板: <placeholder_name/>")
        print("   - 🔢 支持前N个因子选择")
        print("   - 🔄 支持顺序填充和所有组合两种模式")
        print("   - 👁️ 支持公式预览")
        print("   - 📋 支持一键复制")
        print("   - 💾 支持文件导出")
        print("   - 📊 支持逗号分隔格式")
        print("\n💡 使用方法:")
        print("   1. 先进行搜索获取因子数据")
        print("   2. 点击'📝 公式编辑器'按钮")
        print("   3. 输入包含占位符的公式模板")
        print("   4. 选择前N个因子和填充模式")
        print("   5. 生成、预览、复制或导出公式")
    else:
        print("⚠️ 需要先登录并搜索因子数据才能测试公式编辑器")

if __name__ == "__main__":
    main()
