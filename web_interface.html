<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            font-size: 1.1em;
        }
        
        .form-group select,
        .form-group input {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .search-section {
            grid-column: 1 / -1;
        }
        
        .search-section input {
            width: 100%;
            padding: 15px 20px;
            font-size: 1.1em;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .search-section input:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .button-container {
            text-align: center;
            margin-top: 30px;
        }
        
        .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .search-btn:active {
            transform: translateY(0);
        }
        
        .results-container {
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .results-count {
            font-size: 1.1em;
            color: #666;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .results-table th {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .results-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .results-table tr:hover {
            background: #f5f5f5;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #fcc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据字段检索器</h1>
            <p>基于WQB的智能数据字段搜索系统</p>
        </div>
        
        <div class="form-container">
            <form id="searchForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="region">地区 (Region)</label>
                        <select id="region" name="region" required>
                            <option value="USA">USA - 美国</option>
                            <option value="EUR">EUR - 欧洲</option>
                            <option value="ASI">ASI - 亚洲</option>
                            <option value="CHN">CHN - 中国</option>
                            <option value="GLB">GLB - 全球</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="delay">延迟 (Delay)</label>
                        <select id="delay" name="delay" required>
                            <option value="1">1 - 延迟数据</option>
                            <option value="0">0 - 实时数据</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="universe">股票池 (Universe)</label>
                        <select id="universe" name="universe" required>
                            <option value="TOP3000">TOP3000</option>
                            <option value="TOP2500">TOP2500</option>
                            <option value="TOP2000U">TOP2000U</option>
                            <option value="TOP1200">TOP1200</option>
                            <option value="TOP1000">TOP1000</option>
                            <option value="TOP800">TOP800</option>
                            <option value="TOPSP500">TOPSP500</option>
                            <option value="TOP500">TOP500</option>
                            <option value="TOP400">TOP400</option>
                            <option value="TOP200">TOP200</option>
                            <option value="TOPDIV3000">TOPDIV3000</option>
                            <option value="MINVOL1M">MINVOL1M</option>
                            <option value="ILLIQUID_MINVOL1M">ILLIQUID_MINVOL1M</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">数据类型 (Type)</label>
                        <select id="type" name="type" required>
                            <option value="">所有类型</option>
                            <option value="MATRIX">MATRIX - 矩阵数据</option>
                            <option value="VECTOR">VECTOR - 向量数据</option>
                            <option value="GROUP">GROUP - 分组数据</option>
                            <option value="UNIVERSE">UNIVERSE - 股票池数据</option>
                            <option value="SYMBOL">SYMBOL - 符号数据</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="category">数据类别 (Category)</label>
                        <select id="category" name="category" required>
                            <option value="">所有类别</option>
                            <option value="analyst">analyst - 分析师数据</option>
                            <option value="fundamental">fundamental - 基本面数据</option>
                            <option value="model">model - 模型数据</option>
                            <option value="news">news - 新闻数据</option>
                            <option value="option">option - 期权数据</option>
                            <option value="other">other - 其他数据</option>
                            <option value="pv">pv - 价格成交量数据</option>
                            <option value="risk">risk - 风险数据</option>
                            <option value="sentiment">sentiment - 情绪数据</option>
                            <option value="shortinterest">shortinterest - 空头利息</option>
                            <option value="socialmedia">socialmedia - 社交媒体</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">最大结果数 (Limit)</label>
                        <select id="limit" name="limit">
                            <option value="100">100</option>
                            <option value="500">500</option>
                            <option value="1000" selected>1000</option>
                            <option value="2000">2000</option>
                            <option value="5000">5000</option>
                        </select>
                    </div>
                    
                    <div class="form-group search-section">
                        <label for="search">搜索关键词 (Search)</label>
                        <input type="text" id="search" name="search" placeholder="输入搜索关键词，如：quarterly earnings, open, price, volume等">
                    </div>
                </div>
                
                <div class="button-container">
                    <button type="submit" class="search-btn">🔍 开始搜索</button>
                </div>
            </form>
            
            <div id="results" class="results-container">
                <div class="results-header">
                    <h3>搜索结果</h3>
                    <span id="resultsCount" class="results-count"></span>
                </div>
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = Object.fromEntries(formData.entries());
            
            // 显示加载状态
            const resultsContainer = document.getElementById('results');
            const resultsContent = document.getElementById('resultsContent');
            resultsContainer.style.display = 'block';
            resultsContent.innerHTML = '<div class="loading">正在搜索数据...</div>';
            
            try {
                const response = await fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(params)
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultsContent.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                    return;
                }
                
                // 显示结果
                document.getElementById('resultsCount').textContent = `找到 ${data.results.length} 条结果`;
                
                if (data.results.length === 0) {
                    resultsContent.innerHTML = '<p>没有找到匹配的结果，请尝试调整搜索条件。</p>';
                    return;
                }
                
                // 创建表格
                let tableHTML = `
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>字段ID</th>
                                <th>描述</th>
                                <th>类型</th>
                                <th>覆盖率</th>
                                <th>数据集</th>
                                <th>类别</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.results.forEach(item => {
                    tableHTML += `
                        <tr>
                            <td><strong>${item.id}</strong></td>
                            <td>${item.description}</td>
                            <td>${item.type}</td>
                            <td>${(item.coverage * 100).toFixed(1)}%</td>
                            <td>${item['dataset.name']}</td>
                            <td>${item['category.name']}</td>
                        </tr>
                    `;
                });
                
                tableHTML += '</tbody></table>';
                resultsContent.innerHTML = tableHTML;
                
            } catch (error) {
                resultsContent.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
