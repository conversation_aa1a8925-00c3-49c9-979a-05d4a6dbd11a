#!/usr/bin/env python3
"""
测试Region、Delay、Universe参数是否正确传递
"""

import requests
import json
import time

def test_region_parameter():
    """测试不同Region参数"""
    print("🌍 测试Region参数传递...")
    
    regions = ['USA', 'EUR', 'ASI', 'CHN', 'GLB']
    
    for region in regions:
        print(f"\n--- 测试Region: {region} ---")
        
        test_data = {
            'region': region,
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 10,  # 只要10个结果用于快速测试
            'use_api': True
        }
        
        print(f"📤 发送参数: region='{region}'")
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    if results:
                        # 检查返回结果的region字段
                        first_result = results[0]
                        actual_region = first_result.get('region', 'N/A')
                        
                        print(f"📥 返回结果数: {len(results)}")
                        print(f"🎯 期望Region: {region}")
                        print(f"📊 实际Region: {actual_region}")
                        
                        if actual_region == region:
                            print(f"✅ Region参数正确传递！")
                        else:
                            print(f"❌ Region参数传递有问题！")
                            print(f"💡 可能的原因:")
                            print(f"   - API调用参数错误")
                            print(f"   - API内部逻辑问题")
                            print(f"   - 数据本身的region字段不正确")
                        
                        # 显示前3个结果的详细信息
                        print(f"🔍 前3个结果详情:")
                        for i, item in enumerate(results[:3], 1):
                            print(f"   {i}. ID: {item.get('id', 'N/A')}")
                            print(f"      Region: {item.get('region', 'N/A')}")
                            print(f"      Description: {item.get('description', 'N/A')[:50]}...")
                    else:
                        print(f"⚠️ 没有返回结果")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            elif response.status_code == 401:
                print("❌ 需要先在浏览器中登录API账户")
                return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)  # 避免请求过快
    
    return True

def test_delay_parameter():
    """测试Delay参数"""
    print("\n⏱️ 测试Delay参数传递...")
    
    delays = [0, 1]
    delay_names = {0: '实时数据', 1: '延迟数据'}
    
    for delay in delays:
        print(f"\n--- 测试Delay: {delay} ({delay_names[delay]}) ---")
        
        test_data = {
            'region': 'USA',
            'delay': delay,
            'universe': 'TOP3000',
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True
        }
        
        print(f"📤 发送参数: delay={delay}")
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    if results:
                        print(f"📥 返回结果数: {len(results)}")
                        print(f"✅ Delay={delay} 参数传递成功")
                        
                        # 检查结果中是否有delay相关信息
                        first_result = results[0]
                        if 'delay' in first_result:
                            actual_delay = first_result.get('delay')
                            print(f"📊 结果中的delay字段: {actual_delay}")
                        else:
                            print(f"💡 结果中没有delay字段（这可能是正常的）")
                    else:
                        print(f"⚠️ 没有返回结果")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)

def test_universe_parameter():
    """测试Universe参数"""
    print("\n🌐 测试Universe参数传递...")
    
    universes = ['TOP3000', 'TOP1000', 'MINVOL1M']
    
    for universe in universes:
        print(f"\n--- 测试Universe: {universe} ---")
        
        test_data = {
            'region': 'USA',
            'delay': 1,
            'universe': universe,
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True
        }
        
        print(f"📤 发送参数: universe='{universe}'")
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    if results:
                        print(f"📥 返回结果数: {len(results)}")
                        print(f"✅ Universe={universe} 参数传递成功")
                        
                        # 检查结果中是否有universe相关信息
                        first_result = results[0]
                        if 'universe' in first_result:
                            actual_universe = first_result.get('universe')
                            print(f"📊 结果中的universe字段: {actual_universe}")
                        else:
                            print(f"💡 结果中没有universe字段（这可能是正常的）")
                    else:
                        print(f"⚠️ 没有返回结果")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)

def main():
    print("=" * 60)
    print("🔧 Region、Delay、Universe参数测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常")
    print("💡 请确保已在浏览器中登录API账户")
    print("💡 测试过程中请查看Flask控制台的详细日志\n")
    
    # 执行测试
    region_ok = test_region_parameter()
    
    if region_ok:
        test_delay_parameter()
        test_universe_parameter()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if region_ok:
        print("🎉 参数测试完成！")
        print("\n💡 如果发现Region参数问题:")
        print("   1. 检查Flask控制台的详细日志")
        print("   2. 确认API调用参数是否正确传递")
        print("   3. 验证返回结果的region字段")
        print("   4. 可能需要检查WQB API的实际行为")
    else:
        print("⚠️ 需要先登录才能完成测试")

if __name__ == "__main__":
    main()
