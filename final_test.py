#!/usr/bin/env python3
"""
最终测试脚本 - 验证所有功能
"""

import requests
import json
import time

def test_local_search():
    """测试本地搜索"""
    print("1️⃣ 测试本地搜索功能")
    print("-" * 40)
    
    test_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 5,
        'use_api': False
    }
    
    start_time = time.time()
    
    try:
        response = requests.post('http://localhost:5000/search', json=test_data, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 本地搜索成功！")
                print(f"⏱️  响应时间: {end_time - start_time:.2f} 秒")
                print(f"📊 找到结果: {result.get('count', 0)} 个")
                print(f"📋 返回结果: {len(result.get('results', []))} 个")
                
                if result.get('results'):
                    first = result['results'][0]
                    print(f"🔍 示例: {first.get('id', 'N/A')} - {first.get('description', 'N/A')[:50]}...")
                return True
            else:
                print(f"❌ 本地搜索失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

def test_api_search():
    """测试API搜索"""
    print("\n2️⃣ 测试API搜索功能")
    print("-" * 40)
    
    test_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'earnings',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 5,
        'use_api': True
    }
    
    start_time = time.time()
    
    try:
        response = requests.post('http://localhost:5000/search', json=test_data, timeout=45)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API搜索成功！")
                print(f"⏱️  响应时间: {end_time - start_time:.2f} 秒")
                print(f"📊 找到结果: {result.get('count', 0)} 个")
                print(f"📋 返回结果: {len(result.get('results', []))} 个")
                
                if result.get('results'):
                    first = result['results'][0]
                    category = first.get('category', {})
                    category_name = category.get('name', 'N/A') if isinstance(category, dict) else str(category)
                    print(f"🔍 示例: {first.get('id', 'N/A')} - {first.get('description', 'N/A')[:50]}...")
                    print(f"📂 类别: {category_name}")
                return True
            else:
                print(f"❌ API搜索失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n3️⃣ 测试API连接")
    print("-" * 40)
    
    try:
        response = requests.get('http://localhost:5000/test_connection', timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API连接成功！")
                print(f"👤 用户ID: {result.get('user_id', 'N/A')}")
                return True
            else:
                print(f"❌ API连接失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def test_different_categories():
    """测试不同类别的搜索"""
    print("\n4️⃣ 测试不同类别搜索")
    print("-" * 40)
    
    categories = ['analyst', 'fundamental', 'model', 'pv']
    success_count = 0
    
    for category in categories:
        test_data = {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'search': '',
            'category': category,
            'type': 'MATRIX',
            'limit': 3,
            'use_api': False
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    count = result.get('count', 0)
                    print(f"✅ {category}: {count} 个结果")
                    success_count += 1
                else:
                    print(f"❌ {category}: 搜索失败")
            else:
                print(f"❌ {category}: HTTP错误")
        except Exception as e:
            print(f"❌ {category}: 请求错误")
    
    return success_count == len(categories)

def main():
    print("=" * 60)
    print("🧪 数据字段检索器 - 最终功能测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常，开始测试...\n")
    
    # 执行所有测试
    local_success = test_local_search()
    api_success = test_api_search()
    connection_success = test_api_connection()
    category_success = test_different_categories()
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    tests = [
        ("本地搜索", local_success),
        ("API搜索", api_success),
        ("API连接", connection_success),
        ("类别搜索", category_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name:<10}: {status}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！数据字段检索器功能完全正常！")
        print("\n💡 使用说明:")
        print("   1. 在浏览器中访问: http://localhost:5000")
        print("   2. 本地搜索：快速，适合日常使用")
        print("   3. API搜索：较慢（15-30秒），但数据最新")
        print("   4. 支持多种筛选条件和搜索模式")
    else:
        print(f"\n⚠️  部分测试失败，但核心功能可用")
        if local_success:
            print("   ✅ 本地搜索功能正常，可以正常使用")
        if api_success:
            print("   ✅ API搜索功能正常，可以获取最新数据")

if __name__ == "__main__":
    main()
