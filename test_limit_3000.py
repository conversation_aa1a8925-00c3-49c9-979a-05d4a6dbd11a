#!/usr/bin/env python3
"""
测试API limit=3000的功能
"""

import requests
import json
import time

def test_login_and_search():
    """测试登录和搜索功能"""
    print("🔐 测试登录和API搜索（limit=3000）...")
    
    # 1. 先登录
    print("\n1️⃣ 测试登录...")
    username = input("请输入WQB用户名/邮箱: ").strip()
    password = input("请输入WQB密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return False
    
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post('http://localhost:5000/login', json=login_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功！用户ID: {result.get('user_id')}")
            else:
                print(f"❌ 登录失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 登录HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录请求错误: {e}")
        return False
    
    # 2. 测试不同的用户limit值
    print("\n2️⃣ 测试不同的用户limit值...")
    
    test_cases = [
        {'limit': 5, 'name': '返回5个结果'},
        {'limit': 20, 'name': '返回20个结果'},
        {'limit': 100, 'name': '返回100个结果'}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}")
        print("-" * 40)
        
        search_data = {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': test_case['limit'],
            'use_api': True
        }
        
        start_time = time.time()
        
        try:
            response = requests.post('http://localhost:5000/search', json=search_data, timeout=90)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    actual_count = len(result.get('results', []))
                    expected_count = test_case['limit']
                    
                    print(f"⏱️  响应时间: {end_time - start_time:.2f} 秒")
                    print(f"🎯 期望结果: {expected_count} 个")
                    print(f"📋 实际结果: {actual_count} 个")
                    print(f"🔧 API内部limit: 3000 (固定)")
                    
                    if actual_count == expected_count:
                        print(f"✅ 用户limit={expected_count} 正常工作！")
                    elif actual_count < expected_count:
                        print(f"⚠️ 实际结果少于期望，可能是API返回的数据不足")
                    else:
                        print(f"❌ 实际结果多于期望，limit控制有问题")
                    
                    # 显示前3个结果
                    results = result.get('results', [])
                    for j, item in enumerate(results[:3], 1):
                        print(f"   {j}. {item.get('id', 'N/A')} - {item.get('description', 'N/A')[:50]}...")
                        
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            else:
                print(f"❌ 搜索HTTP错误: {response.status_code}")
                if response.status_code == 401:
                    print("💡 会话可能已过期，请重新运行测试")
                    return False
                
        except Exception as e:
            print(f"❌ 搜索请求错误: {e}")
        
        if i < len(test_cases):
            time.sleep(2)  # 避免请求过快
    
    return True

def main():
    print("=" * 60)
    print("🧪 API Limit=3000 功能测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务，请确保Flask应用正在运行")
        return
    
    print("✅ 服务运行正常，开始测试...\n")
    
    success = test_login_and_search()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 测试完成！")
        print("\n💡 新的工作方式:")
        print("   ✅ API调用使用固定的limit=3000")
        print("   ✅ 用户的limit参数控制返回结果数量")
        print("   ✅ 确保获得足够数据的同时控制返回量")
        print("   ✅ 提高了数据获取的效率和完整性")
    else:
        print("⚠️ 测试未完全成功，请检查相关问题")

if __name__ == "__main__":
    main()
