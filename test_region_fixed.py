#!/usr/bin/env python3
"""
测试修复后的Region参数功能
"""

import requests
import json
import time

def test_valid_regions():
    """测试有效的Region参数"""
    print("🌍 测试有效的Region参数...")
    
    valid_regions = ['USA', 'CHN', 'GLB']
    
    for region in valid_regions:
        print(f"\n--- 测试Region: {region} ---")
        
        test_data = {
            'region': region,
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 10,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    if results:
                        # 检查返回结果的region字段
                        first_result = results[0]
                        actual_region = first_result.get('region', 'N/A')
                        
                        print(f"📤 请求Region: {region}")
                        print(f"📥 返回结果数: {len(results)}")
                        print(f"📊 实际Region: {actual_region}")
                        
                        if actual_region == region:
                            print(f"✅ Region参数正确工作！")
                        else:
                            print(f"❌ Region不匹配！")
                        
                        # 验证所有结果的region一致性
                        region_set = set(r.get('region', 'N/A') for r in results)
                        if len(region_set) == 1 and list(region_set)[0] == region:
                            print(f"✅ 所有结果的Region都一致")
                        else:
                            print(f"⚠️ 结果中有不同的Region: {region_set}")
                    else:
                        print(f"⚠️ 没有返回结果")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            elif response.status_code == 401:
                print("❌ 需要先在浏览器中登录API账户")
                return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)
    
    return True

def test_invalid_regions():
    """测试无效的Region参数（应该被拒绝）"""
    print("\n🚫 测试无效的Region参数...")
    
    invalid_regions = ['EUR', 'ASI', 'INVALID']
    
    for region in invalid_regions:
        print(f"\n--- 测试无效Region: {region} ---")
        
        test_data = {
            'region': region,
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 10,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=30)
            
            if response.status_code == 400:
                result = response.json()
                error_msg = result.get('error', '')
                print(f"✅ 正确拒绝无效Region: {region}")
                print(f"💬 错误消息: {error_msg}")
            elif response.status_code == 200:
                print(f"❌ 应该拒绝无效Region，但请求成功了")
            else:
                print(f"⚠️ 意外的HTTP状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(1)

def test_region_data_consistency():
    """测试不同Region返回数据的一致性"""
    print("\n🔍 测试不同Region数据的一致性...")
    
    regions = ['USA', 'CHN', 'GLB']
    region_results = {}
    
    for region in regions:
        test_data = {
            'region': region,
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'close',  # 使用常见的字段
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    region_results[region] = results
                    print(f"📊 {region}: {len(results)} 个结果")
                else:
                    print(f"❌ {region}: 搜索失败")
            else:
                print(f"❌ {region}: HTTP错误 {response.status_code}")
                
        except Exception as e:
            print(f"❌ {region}: 请求错误 {e}")
        
        time.sleep(2)
    
    # 分析结果
    print(f"\n📈 数据一致性分析:")
    for region, results in region_results.items():
        if results:
            # 检查region字段
            regions_in_results = set(r.get('region', 'N/A') for r in results)
            print(f"   {region}: 结果中的region字段 = {regions_in_results}")
            
            # 显示一些字段ID
            ids = [r.get('id', 'N/A') for r in results[:3]]
            print(f"   {region}: 前3个字段ID = {ids}")

def main():
    print("=" * 60)
    print("🔧 Region参数修复验证测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常")
    print("💡 请确保已在浏览器中登录API账户\n")
    
    # 执行测试
    valid_ok = test_valid_regions()
    
    if valid_ok:
        test_invalid_regions()
        test_region_data_consistency()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if valid_ok:
        print("🎉 Region参数修复测试完成！")
        print("\n✅ 修复内容:")
        print("   - 移除了无效的Region: EUR, ASI")
        print("   - 只保留有效的Region: USA, CHN, GLB")
        print("   - 添加了Region参数验证")
        print("   - 现在Region参数完全正确工作")
        print("\n💡 现在用户选择的Region与返回的数据完全一致！")
    else:
        print("⚠️ 需要先登录才能完成测试")

if __name__ == "__main__":
    main()
