import pandas as pd
import os
from pathlib import Path

def extract_categories_from_csv(file_path):
    """从CSV文件中提取category信息"""
    try:
        df = pd.read_csv(file_path)
        if 'category.id' in df.columns and 'category.name' in df.columns:
            categories = df[['category.id', 'category.name']].drop_duplicates()
            return categories
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    return pd.DataFrame()

def main():
    # 数据文件夹路径
    data_folder = Path('data/split_files')
    
    all_categories = pd.DataFrame()
    
    # 遍历所有CSV文件
    for csv_file in data_folder.glob('*.csv'):
        print(f"Processing {csv_file.name}...")
        categories = extract_categories_from_csv(csv_file)
        if not categories.empty:
            all_categories = pd.concat([all_categories, categories], ignore_index=True)
    
    # 去重并排序
    unique_categories = all_categories.drop_duplicates().sort_values('category.id')
    
    print("\n所有唯一的Category信息:")
    print("=" * 50)
    for _, row in unique_categories.iterrows():
        print(f"ID: {row['category.id']:<15} Name: {row['category.name']}")
    
    # 保存到文件
    unique_categories.to_csv('unique_categories.csv', index=False)
    print(f"\n结果已保存到 unique_categories.csv")
    print(f"总共找到 {len(unique_categories)} 个唯一的category")

if __name__ == "__main__":
    main()
