#!/usr/bin/env python3
"""
简单的API搜索测试 - 使用较小的limit来减少响应时间
"""

import requests
import json
import time

def test_api_search_simple():
    """测试简单的API搜索"""
    print("🧪 测试简单API搜索（小数据量）...")
    
    # 使用较小的limit来减少响应时间
    test_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',  # 简单搜索词
        'category': '',     # 不限制类别
        'type': 'MATRIX',   # 限制类型
        'limit': 3,         # 只要3个结果
        'use_api': True
    }
    
    print("搜索参数:", json.dumps(test_data, indent=2))
    
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://localhost:5000/search', 
            json=test_data, 
            timeout=45  # 45秒超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  响应时间: {duration:.2f} 秒")
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"✅ API搜索成功！")
                print(f"📈 找到结果数: {result.get('count', 0)}")
                print(f"📋 返回结果数: {len(result.get('results', []))}")
                
                # 显示结果
                results = result.get('results', [])
                for i, item in enumerate(results, 1):
                    print(f"\n{i}. {item.get('id', 'N/A')}")
                    print(f"   📝 描述: {item.get('description', 'N/A')[:80]}...")
                    
                    # 处理嵌套的category对象
                    category = item.get('category', {})
                    if isinstance(category, dict):
                        category_name = category.get('name', 'N/A')
                    else:
                        category_name = str(category)
                    
                    print(f"   📂 类别: {category_name}")
                    print(f"   📊 类型: {item.get('type', 'N/A')}")
                    print(f"   📈 覆盖率: {item.get('coverage', 'N/A')}")
                    print(f"   👥 用户数: {item.get('userCount', 'N/A')}")
                    print(f"   🔢 Alpha数: {item.get('alphaCount', 'N/A')}")
                
                return True
            else:
                print(f"❌ API搜索失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误 {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时（45秒）")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_local_search_comparison():
    """对比测试本地搜索"""
    print("\n🔄 对比测试本地搜索...")
    
    test_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': '',
        'type': 'MATRIX',
        'limit': 3,
        'use_api': False  # 本地搜索
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://localhost:5000/search', 
            json=test_data, 
            timeout=10
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  本地搜索响应时间: {duration:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 本地搜索成功！找到 {result.get('count', 0)} 个结果")
                return True
            else:
                print(f"❌ 本地搜索失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 本地搜索HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 本地搜索异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🔍 数据字段检索器 - API搜索测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常\n")
    
    # 先测试本地搜索作为对比
    local_success = test_local_search_comparison()
    
    # 测试API搜索
    api_success = test_api_search_simple()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   本地搜索: {'✅ 成功' if local_success else '❌ 失败'}")
    print(f"   API搜索:  {'✅ 成功' if api_success else '❌ 失败'}")
    
    if api_success:
        print("\n🎉 API搜索功能正常！")
        print("💡 建议:")
        print("   - API搜索响应较慢，适合获取最新数据")
        print("   - 本地搜索响应快速，适合日常使用")
        print("   - 可以在网页中选择合适的搜索模式")
    else:
        print("\n⚠️  API搜索可能需要更长时间或有其他问题")
        print("💡 建议使用本地搜索功能")

if __name__ == "__main__":
    main()
