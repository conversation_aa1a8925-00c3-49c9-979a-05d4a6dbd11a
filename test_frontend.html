<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>数据字段检索器 - 前端测试</h1>
    
    <div class="test-section">
        <h2>测试1: 基本搜索功能</h2>
        <button onclick="testBasicSearch()">测试基本搜索</button>
        <div id="test1-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 带筛选条件的搜索</h2>
        <button onclick="testFilteredSearch()">测试筛选搜索</button>
        <div id="test2-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: API连接测试</h2>
        <button onclick="testApiConnection()">测试API连接</button>
        <div id="test3-result" class="result"></div>
    </div>

    <script>
        async function testBasicSearch() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        region: 'USA',
                        delay: 1,
                        universe: 'TOP3000',
                        search: 'price',
                        category: '',
                        type: '',
                        limit: 3,
                        use_api: false
                    })
                });
                
                const data = await response.json();
                console.log('基本搜索结果:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 搜索成功！</div>
                        <p>找到 ${data.count} 个结果</p>
                        <p>第一个结果: ${data.results[0]?.id} - ${data.results[0]?.description}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 搜索失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('基本搜索错误:', error);
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testFilteredSearch() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        region: 'USA',
                        delay: 1,
                        universe: 'TOP3000',
                        search: 'earnings',
                        category: 'analyst',
                        type: 'MATRIX',
                        limit: 3,
                        use_api: false
                    })
                });
                
                const data = await response.json();
                console.log('筛选搜索结果:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 筛选搜索成功！</div>
                        <p>找到 ${data.count} 个结果</p>
                        <p>第一个结果: ${data.results[0]?.id} - ${data.results[0]?.description}</p>
                        <p>类别: ${data.results[0]?.['category.name']}</p>
                        <p>类型: ${data.results[0]?.type}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 筛选搜索失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('筛选搜索错误:', error);
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testApiConnection() {
            const resultDiv = document.getElementById('test3-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('/test_connection');
                const data = await response.json();
                console.log('API连接结果:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ API连接成功！</div>
                        <p>用户ID: ${data.user_id}</p>
                        <p>状态码: ${data.status_code}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API连接失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('API连接错误:', error);
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('前端测试页面已加载');
        };
    </script>
</body>
</html>
