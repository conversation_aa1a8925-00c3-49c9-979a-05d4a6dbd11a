#!/usr/bin/env python3
"""
数据字段检索器启动脚本
自动处理依赖安装和环境配置
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """显示启动横幅"""
    print("=" * 60)
    print("🔍 数据字段检索器 - 自动启动脚本")
    print("=" * 60)
    print("🚀 正在检查环境和依赖...")

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    else:
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")

def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用，请先安装pip")
        return False

def fix_googletrans_compatibility():
    """修复googletrans兼容性问题"""
    print("🔧 修复翻译库兼容性...")
    try:
        # 卸载可能冲突的包
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "-y", "googletrans"],
                      capture_output=True)
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "-y", "httpcore"],
                      capture_output=True)
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "-y", "httpx"],
                      capture_output=True)

        # 安装兼容版本
        compatible_packages = [
            "httpcore==0.9.1",
            "httpx==0.13.3",
            "googletrans==3.1.0a0"
        ]

        for package in compatible_packages:
            print(f"   安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package],
                                   capture_output=True, text=True)
            if result.returncode != 0:
                print(f"   ⚠️ {package} 安装失败，尝试备用方案...")

        return True
    except Exception as e:
        print(f"❌ 兼容性修复失败: {e}")
        return False

def install_requirements():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 未找到requirements.txt文件")
        return False

    print("📦 正在安装依赖包...")
    try:
        # 升级pip
        print("   升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)

        # 先修复googletrans兼容性
        fix_googletrans_compatibility()

        # 安装其他依赖
        print("   安装项目依赖...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                               capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 依赖安装成功")
            return True
        else:
            print("⚠️ 部分依赖安装可能有问题，但继续尝试启动...")
            print("详细错误信息:")
            print(result.stderr)
            return True  # 继续尝试启动，可能核心功能仍可用

    except subprocess.CalledProcessError as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def check_dependencies():
    """检查关键依赖是否已安装"""
    required_packages = [
        'flask',
        'pandas', 
        'wqb',
        'googletrans',
        'requests',
        'urllib3'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    return len(missing_packages) == 0, missing_packages

def check_config_file():
    """检查配置文件"""
    config_file = Path("config.py")
    config_example = Path("config_example.py")
    
    if not config_file.exists():
        if config_example.exists():
            print("⚠️  未找到config.py配置文件")
            print("💡 请复制config_example.py为config.py并填入您的账户信息:")
            print("   cp config_example.py config.py")
            print("   然后编辑config.py文件")
            return False
        else:
            print("❌ 未找到配置文件模板")
            return False
    else:
        print("✅ 配置文件存在")
        return True

def start_application():
    """启动Flask应用"""
    print("\n🚀 启动数据字段检索器...")
    print("📍 访问地址: http://127.0.0.1:5000")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 60)

    try:
        # 设置环境变量避免重复检查
        os.environ['SKIP_DEPENDENCY_CHECK'] = '1'

        # 导入并运行app
        import app
        app.app.run(host='0.0.0.0', port=5000, debug=True)
    except ImportError as e:
        print(f"❌ 无法导入应用: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    # 如果是Flask重载进程，直接退出
    if os.environ.get('SKIP_DEPENDENCY_CHECK'):
        return

    print_banner()

    # 1. 检查Python版本
    check_python_version()

    # 2. 检查pip
    if not check_pip():
        sys.exit(1)

    # 3. 检查依赖
    deps_ok, missing = check_dependencies()

    # 4. 如果有缺失依赖，自动安装
    if not deps_ok:
        print(f"\n📦 发现缺失依赖: {', '.join(missing)}")
        print("🔄 正在自动安装...")

        if not install_requirements():
            print("\n❌ 自动安装失败，请手动安装:")
            print("   pip install -r requirements.txt")
            sys.exit(1)

        # 重新检查
        deps_ok, missing = check_dependencies()
        if not deps_ok:
            print(f"❌ 仍有依赖缺失: {', '.join(missing)}")
            sys.exit(1)

    # 5. 检查配置文件
    if not check_config_file():
        print("\n⚠️  配置文件缺失，但应用仍可启动（需要在界面中登录）")
        input("按Enter键继续启动应用...")

    # 6. 启动应用
    print("\n" + "=" * 60)
    start_application()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动脚本出错: {e}")
        sys.exit(1)
