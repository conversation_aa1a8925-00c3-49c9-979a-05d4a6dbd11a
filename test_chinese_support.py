#!/usr/bin/env python3
"""
测试中文支持功能
"""

import requests
import time

def test_chinese_detection():
    """测试中文检测功能"""
    print("🔤 测试中文检测功能...")
    
    # 导入检测函数
    import sys
    sys.path.append('.')
    from app import contains_chinese
    
    test_cases = [
        ("price", False, "纯英文"),
        ("价格", True, "纯中文"),
        ("price价格", True, "中英混合"),
        ("123", False, "纯数字"),
        ("", False, "空字符串"),
        ("市盈率PE", True, "中文+英文缩写")
    ]
    
    for text, expected, description in test_cases:
        result = contains_chinese(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {description}: '{text}' -> {result}")

def test_translation():
    """测试翻译功能"""
    print("\n🌐 测试翻译功能...")
    
    # 导入翻译函数
    import sys
    sys.path.append('.')
    from app import translate_text
    
    test_cases = [
        ("价格", "zh", "en", "中文->英文"),
        ("成交量", "zh", "en", "中文->英文"),
        ("市值", "zh", "en", "中文->英文"),
        ("price", "en", "zh", "英文->中文"),
        ("volume", "en", "zh", "英文->中文"),
        ("market cap", "en", "zh", "英文->中文")
    ]
    
    for text, src, dest, description in test_cases:
        try:
            result = translate_text(text, src=src, dest=dest)
            print(f"  ✅ {description}: '{text}' -> '{result}'")
        except Exception as e:
            print(f"  ❌ {description}: '{text}' -> 翻译失败: {e}")
        time.sleep(0.5)  # 避免API限制

def test_chinese_search():
    """测试中文搜索功能"""
    print("\n🔍 测试中文搜索功能...")
    
    chinese_search_terms = [
        "价格",
        "成交量", 
        "市值",
        "收益率",
        "波动率"
    ]
    
    for search_term in chinese_search_terms:
        print(f"\n--- 测试搜索: {search_term} ---")
        
        test_data = {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'search': search_term,
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True,
            'translate_results': False  # 先不翻译结果
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    print(f"✅ 搜索成功: 找到 {len(results)} 个结果")
                    
                    # 显示前3个结果
                    for i, item in enumerate(results[:3], 1):
                        print(f"   {i}. {item.get('id', 'N/A')} - {item.get('description', 'N/A')[:50]}...")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            elif response.status_code == 401:
                print("⚠️ 需要先登录API账户")
                return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)  # 避免请求过快
    
    return True

def test_result_translation():
    """测试结果翻译功能"""
    print("\n🔤 测试结果翻译功能...")
    
    # 先进行一次英文搜索获取结果
    search_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 3,
        'use_api': True,
        'translate_results': False
    }
    
    try:
        response = requests.post('http://localhost:5000/search', json=search_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                results = result.get('results', [])
                print(f"✅ 获得 {len(results)} 个搜索结果")
                
                # 测试翻译这些结果
                translate_data = {'results': results}
                
                translate_response = requests.post('http://localhost:5000/translate_results', 
                                                 json=translate_data, timeout=60)
                
                if translate_response.status_code == 200:
                    translate_result = translate_response.json()
                    if translate_result.get('success'):
                        translated_results = translate_result.get('translated_results', [])
                        print(f"✅ 翻译成功: {len(translated_results)} 个结果")
                        
                        # 显示翻译结果
                        for i, item in enumerate(translated_results[:2], 1):
                            print(f"   {i}. {item.get('id', 'N/A')}")
                            print(f"      原文: {item.get('description_original', 'N/A')[:50]}...")
                            print(f"      译文: {item.get('description_translated', 'N/A')[:50]}...")
                    else:
                        print(f"❌ 翻译失败: {translate_result.get('error')}")
                else:
                    print(f"❌ 翻译请求失败: {translate_response.status_code}")
            else:
                print(f"❌ 搜索失败: {result.get('error')}")
        else:
            print(f"❌ 搜索请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    print("=" * 60)
    print("🌐 中文支持功能测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常")
    print("💡 请确保已在浏览器中登录API账户\n")
    
    # 执行测试
    test_chinese_detection()
    test_translation()
    
    search_ok = test_chinese_search()
    if search_ok:
        test_result_translation()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if search_ok:
        print("🎉 中文支持功能测试完成！")
        print("\n✅ 功能验证:")
        print("   - 中文字符检测 ✅")
        print("   - 中英文翻译 ✅")
        print("   - 中文搜索词自动翻译 ✅")
        print("   - 搜索结果翻译 ✅")
        print("   - 双语结果显示 ✅")
        print("\n💡 使用方法:")
        print("   1. 直接输入中文搜索词，如'价格'、'成交量'")
        print("   2. 开启'🌐 中文翻译'开关")
        print("   3. 点击'🔤 翻译描述'获得中文描述")
        print("   4. 享受双语搜索体验！")
    else:
        print("⚠️ 需要先登录才能完成完整测试")
        print("但基础翻译功能已验证正常")

if __name__ == "__main__":
    main()
