<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-search"></i> 数据字段检索器</h4>
                </div>
                
                <form id="searchForm">
                    <div class="mb-3">
                        <label for="region" class="form-label">Region</label>
                        <select class="form-select" id="region" name="region">
                            {% for region in regions %}
                            <option value="{{ region }}" {% if region == 'USA' %}selected{% endif %}>{{ region }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="delay" class="form-label">Delay</label>
                        <select class="form-select" id="delay" name="delay">
                            {% for delay in delays %}
                            <option value="{{ delay }}" {% if delay == 1 %}selected{% endif %}>{{ delay }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="universe" class="form-label">Universe</label>
                        <select class="form-select" id="universe" name="universe">
                            {% for universe in universes %}
                            <option value="{{ universe }}" {% if universe == 'TOP3000' %}selected{% endif %}>{{ universe }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="输入搜索关键词...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">全部</option>
                            {% for cat in categories %}
                            <option value="{{ cat.id }}">{{ cat.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">全部</option>
                            {% for type in types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="limit" class="form-label">最大结果数</label>
                        <input type="number" class="form-control" id="limit" name="limit" value="100" min="1" max="1000">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="use_api" name="use_api">
                            <label class="form-check-label" for="use_api">
                                使用API搜索（否则使用本地数据）
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </form>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-info w-100" id="testConnection">
                        <i class="fas fa-plug"></i> 测试API连接
                    </button>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9 main-content">
                <div class="content-header">
                    <h2>搜索结果</h2>
                    <div id="resultCount" class="text-muted"></div>
                </div>
                
                <div id="loading" class="text-center" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>搜索中...</p>
                </div>
                
                <div id="error" class="alert alert-danger" style="display: none;"></div>
                
                <div id="results" class="results-container">
                    <div class="welcome-message">
                        <i class="fas fa-info-circle"></i>
                        <h4>欢迎使用数据字段检索器</h4>
                        <p>请在左侧设置搜索条件，然后点击搜索按钮开始查找数据字段。</p>
                        <ul>
                            <li><strong>Region:</strong> 选择数据区域（USA, EUR, CHN, ASI, GLB）</li>
                            <li><strong>Delay:</strong> 数据延迟（0或1）</li>
                            <li><strong>Universe:</strong> 数据集范围</li>
                            <li><strong>搜索关键词:</strong> 在字段描述中搜索</li>
                            <li><strong>Category:</strong> 数据大类筛选</li>
                            <li><strong>Type:</strong> 数据类型筛选</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
