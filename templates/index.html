<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { background-color: #343a40; color: white; min-height: 100vh; padding: 20px; }
        .sidebar h4 { color: #17a2b8; margin-bottom: 30px; text-align: center; }
        .sidebar .form-label { color: #adb5bd; font-weight: 500; }
        .sidebar .form-control, .sidebar .form-select {
            background-color: #495057; border: 1px solid #6c757d; color: white;
        }
        .sidebar .form-control:focus, .sidebar .form-select:focus {
            background-color: #495057; border-color: #17a2b8; color: white;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        .sidebar .form-control::placeholder { color: #adb5bd; }
        .sidebar .form-check-label { color: #adb5bd; }
        .main-content { padding: 20px; }
        .content-header {
            background: white; padding: 20px; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;
        }
        .results-container {
            background: white; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-height: 400px; padding: 20px;
        }
        .result-item {
            border-bottom: 1px solid #e9ecef; padding: 15px 0;
        }
        .result-item:last-child { border-bottom: none; }
        .result-id { font-family: monospace; font-weight: bold; color: #007bff; }
        .result-type {
            background-color: #6c757d; color: white; padding: 2px 8px;
            border-radius: 12px; font-size: 0.8rem; margin-left: 10px;
        }
        .result-type.MATRIX { background-color: #28a745; }
        .result-type.VECTOR { background-color: #17a2b8; }
        .result-type.GROUP { background-color: #ffc107; color: #212529; }
        .result-type.UNIVERSE { background-color: #dc3545; }
        .result-meta {
            display: flex; flex-wrap: wrap; gap: 15px;
            font-size: 0.9rem; color: #6c757d; margin-top: 10px;
        }
        .category-badge {
            background-color: #e9ecef; color: #495057;
            padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;
        }
        .welcome-message { text-align: center; color: #6c757d; padding: 40px; }
        .btn-primary { background-color: #17a2b8; border-color: #17a2b8; }
        .btn-primary:hover { background-color: #138496; border-color: #117a8b; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <h4><i class="fas fa-search"></i> 数据字段检索器</h4>

                <form id="searchForm">
                    <div class="mb-3">
                        <label for="region" class="form-label">Region</label>
                        <select class="form-select" id="region">
                            {% for region in regions %}
                            <option value="{{ region }}" {% if region == 'USA' %}selected{% endif %}>{{ region }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="delay" class="form-label">Delay</label>
                        <select class="form-select" id="delay">
                            {% for delay in delays %}
                            <option value="{{ delay }}" {% if delay == 1 %}selected{% endif %}>{{ delay }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="universe" class="form-label">Universe</label>
                        <select class="form-select" id="universe">
                            {% for universe in universes %}
                            <option value="{{ universe }}" {% if universe == 'TOP3000' %}selected{% endif %}>{{ universe }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" placeholder="输入搜索关键词...">
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category">
                            <option value="">全部</option>
                            {% for cat in categories %}
                            <option value="{{ cat.id }}">{{ cat.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type">
                            <option value="">全部</option>
                            {% for type in types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="limit" class="form-label">最大结果数</label>
                        <input type="number" class="form-control" id="limit" value="20" min="1" max="1000">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="use_api">
                            <label class="form-check-label" for="use_api">
                                使用API搜索（实时数据，较慢）
                            </label>
                        </div>
                        <small class="text-muted">
                            💡 推荐使用本地搜索（快速），API搜索可能需要30-60秒
                        </small>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        🔍 搜索
                    </button>

                    <button type="button" class="btn btn-info w-100 mb-2" id="testBtn">
                        🔌 测试连接
                    </button>

                    <button type="button" class="btn btn-success w-100" id="clearBtn">
                        🗑️ 清空结果
                    </button>
                </form>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 main-content">
                <div class="content-header">
                    <h2>搜索结果</h2>
                    <div id="resultCount" class="text-muted"></div>
                </div>

                <div id="loading" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">搜索中...</p>
                </div>

                <div id="error" class="alert alert-danger" style="display: none;"></div>

                <div id="results" class="results-container">
                    <div class="welcome-message">
                        <h4>🔍 数据字段检索器</h4>
                        <p>请在左侧设置搜索条件，然后点击搜索按钮开始查找数据字段。</p>
                        <div class="text-start mt-4">
                            <h6>使用说明：</h6>
                            <ul class="list-unstyled">
                                <li>📍 <strong>Region:</strong> 数据区域 (USA, EUR, CHN等)</li>
                                <li>⏱️ <strong>Delay:</strong> 数据延迟 (0=实时, 1=延迟)</li>
                                <li>🌐 <strong>Universe:</strong> 股票池范围</li>
                                <li>🔤 <strong>关键词:</strong> 在字段描述中搜索</li>
                                <li>📂 <strong>Category:</strong> 数据大类筛选</li>
                                <li>📊 <strong>Type:</strong> 数据类型筛选</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简化的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('searchForm');
            const testBtn = document.getElementById('testBtn');
            const clearBtn = document.getElementById('clearBtn');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const results = document.getElementById('results');
            const resultCount = document.getElementById('resultCount');

            // 搜索表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // 测试连接
            testBtn.addEventListener('click', testConnection);

            // 清空结果
            clearBtn.addEventListener('click', function() {
                results.innerHTML = `
                    <div class="welcome-message">
                        <h4>🔍 数据字段检索器</h4>
                        <p>请在左侧设置搜索条件，然后点击搜索按钮开始查找数据字段。</p>
                    </div>
                `;
                resultCount.textContent = '';
                hideError();
            });

            function performSearch() {
                const searchData = {
                    region: document.getElementById('region').value,
                    delay: parseInt(document.getElementById('delay').value),
                    universe: document.getElementById('universe').value,
                    search: document.getElementById('search').value,
                    category: document.getElementById('category').value,
                    type: document.getElementById('type').value,
                    limit: parseInt(document.getElementById('limit').value),
                    use_api: document.getElementById('use_api').checked
                };

                console.log('搜索参数:', searchData);
                showLoading();
                hideError();

                // 设置不同的超时时间
                const timeoutMs = searchData.use_api ? 60000 : 10000; // API搜索60秒，本地搜索10秒

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

                fetch('/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(searchData),
                    signal: controller.signal
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    return response.json();
                })
                .then(data => {
                    console.log('搜索结果:', data);
                    hideLoading();
                    if (data.success) {
                        displayResults(data.results, data.count);
                    } else {
                        showError(data.error || '搜索失败');
                    }
                })
                .catch(err => {
                    clearTimeout(timeoutId);
                    console.error('搜索错误:', err);
                    hideLoading();
                    if (err.name === 'AbortError') {
                        showError(`搜索超时 (${timeoutMs/1000}秒)。API搜索可能需要更长时间，请尝试使用本地搜索或减少结果数量。`);
                    } else {
                        showError('网络错误: ' + err.message);
                    }
                });
            }

            function testConnection() {
                testBtn.innerHTML = '🔄 测试中...';
                testBtn.disabled = true;

                fetch('/test_connection')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`✅ API连接成功！\\n用户ID: ${data.user_id}`);
                    } else {
                        alert(`❌ API连接失败: ${data.error}`);
                    }
                })
                .catch(err => {
                    alert('❌ 连接测试失败: ' + err.message);
                })
                .finally(() => {
                    testBtn.innerHTML = '🔌 测试连接';
                    testBtn.disabled = false;
                });
            }

            function displayResults(resultsData, count) {
                resultCount.textContent = `找到 ${count} 个结果`;

                if (!resultsData || resultsData.length === 0) {
                    results.innerHTML = `
                        <div class="welcome-message">
                            <h4>😔 未找到匹配的结果</h4>
                            <p>请尝试调整搜索条件或使用不同的关键词。</p>
                        </div>
                    `;
                    return;
                }

                let html = '';
                resultsData.forEach(item => {
                    const coverage = item.coverage ? (item.coverage * 100).toFixed(1) + '%' : 'N/A';
                    const categoryName = item['category.name'] || (item.category && item.category.name) || 'N/A';
                    const datasetName = item['dataset.name'] || (item.dataset && item.dataset.name) || 'N/A';

                    html += `
                        <div class="result-item">
                            <div class="d-flex align-items-center mb-2">
                                <span class="result-id">${item.id || 'N/A'}</span>
                                <span class="result-type ${item.type || ''}">${item.type || 'N/A'}</span>
                            </div>
                            <div class="mb-2">${item.description || '无描述'}</div>
                            <div class="result-meta">
                                <span class="category-badge">${categoryName}</span>
                                <span>📊 覆盖率: ${coverage}</span>
                                <span>👥 用户: ${item.userCount || item.user_count || 'N/A'}</span>
                                <span>🔢 Alpha: ${item.alphaCount || item.alpha_count || 'N/A'}</span>
                                <span>💾 数据集: ${datasetName}</span>
                            </div>
                        </div>
                    `;
                });

                results.innerHTML = html;
            }

            function showLoading() {
                const isApiSearch = document.getElementById('use_api').checked;
                const loadingText = isApiSearch ?
                    '<p class="mt-2">API搜索中，请耐心等待（可能需要30-60秒）...</p>' :
                    '<p class="mt-2">搜索中...</p>';

                loading.innerHTML = `
                    <div class="spinner-border text-primary" role="status"></div>
                    ${loadingText}
                `;
                loading.style.display = 'block';
                results.style.display = 'none';
            }

            function hideLoading() {
                loading.style.display = 'none';
                results.style.display = 'block';
            }

            function showError(message) {
                error.textContent = message;
                error.style.display = 'block';
                setTimeout(() => error.style.display = 'none', 5000);
            }

            function hideError() {
                error.style.display = 'none';
            }
        });
    </script>
</body>
</html>
