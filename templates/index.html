<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { background-color: #343a40; color: white; min-height: 100vh; padding: 20px; }
        .sidebar h4 { color: #17a2b8; margin-bottom: 30px; text-align: center; }
        .sidebar .form-label { color: #adb5bd; font-weight: 500; }
        .sidebar .form-control, .sidebar .form-select {
            background-color: #495057; border: 1px solid #6c757d; color: white;
        }
        .sidebar .form-control:focus, .sidebar .form-select:focus {
            background-color: #495057; border-color: #17a2b8; color: white;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        .sidebar .form-control::placeholder { color: #adb5bd; }
        .sidebar .form-check-label { color: #adb5bd; }
        .main-content { padding: 20px; }
        .content-header {
            background: white; padding: 20px; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;
        }
        .results-container {
            background: white; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-height: 400px; padding: 20px;
        }
        .result-item {
            border-bottom: 1px solid #e9ecef; padding: 15px 0;
        }
        .result-item:last-child { border-bottom: none; }
        .result-id { font-family: monospace; font-weight: bold; color: #007bff; }
        .result-type {
            background-color: #6c757d; color: white; padding: 2px 8px;
            border-radius: 12px; font-size: 0.8rem; margin-left: 10px;
        }
        .result-type.MATRIX { background-color: #28a745; }
        .result-type.VECTOR { background-color: #17a2b8; }
        .result-type.GROUP { background-color: #ffc107; color: #212529; }
        .result-type.UNIVERSE { background-color: #dc3545; }
        .result-meta {
            display: flex; flex-wrap: wrap; gap: 15px;
            font-size: 0.9rem; color: #6c757d; margin-top: 10px;
        }
        .category-badge {
            background-color: #e9ecef; color: #495057;
            padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;
        }
        .welcome-message { text-align: center; color: #6c757d; padding: 40px; }
        .btn-primary { background-color: #17a2b8; border-color: #17a2b8; }
        .btn-primary:hover { background-color: #138496; border-color: #117a8b; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <h4><i class="fas fa-search"></i> 数据字段检索器</h4>

                <form id="searchForm">
                    <div class="mb-3">
                        <label for="region" class="form-label">Region</label>
                        <select class="form-select" id="region">
                            {% for region in regions %}
                            <option value="{{ region }}" {% if region == 'USA' %}selected{% endif %}>{{ region }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="delay" class="form-label">Delay</label>
                        <select class="form-select" id="delay">
                            {% for delay in delays %}
                            <option value="{{ delay }}" {% if delay == 1 %}selected{% endif %}>{{ delay }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="universe" class="form-label">Universe</label>
                        <select class="form-select" id="universe">
                            {% for universe in universes %}
                            <option value="{{ universe }}" {% if universe == 'TOP3000' %}selected{% endif %}>{{ universe }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" placeholder="输入搜索关键词...">
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category">
                            <option value="">全部</option>
                            {% for cat in categories %}
                            <option value="{{ cat.id }}">{{ cat.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type">
                            <option value="">全部</option>
                            {% for type in types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="limit" class="form-label">返回结果数量</label>
                        <input type="number" class="form-control" id="limit" value="100" min="1" max="5000">
                        <small class="text-muted">最多返回多少个结果 (1-5000，系统会自动收集足够的API响应)</small>
                    </div>

                    <div class="mb-3">
                        <div id="api_status" class="mt-2">
                            <small class="text-warning">⚠️ 需要先登录API账户</small>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        🔍 搜索
                    </button>

                    <!-- 公式编辑器按钮 -->
                    <button type="button" class="btn btn-success w-100 mt-2" id="formulaBtn">
                        📝 公式编辑器
                    </button>

                    <button type="button" class="btn btn-warning w-100 mb-2" id="loginBtn">
                        🔑 API登录
                    </button>

                    <button type="button" class="btn btn-info w-100 mb-2" id="testBtn" disabled>
                        🔌 测试连接
                    </button>

                    <button type="button" class="btn btn-success w-100" id="clearBtn">
                        🗑️ 清空结果
                    </button>
                </form>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 main-content">
                <div class="content-header">
                    <h2>搜索结果</h2>
                    <div id="resultCount" class="text-muted"></div>
                </div>

                <div id="loading" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">搜索中...</p>
                </div>

                <div id="error" class="alert alert-danger" style="display: none;"></div>

                <div id="results" class="results-container">
                    <div class="welcome-message">
                        <h4>🔍 WQB数据字段检索器</h4>
                        <p>欢迎使用WQB数据字段检索器！请先登录您的API账户开始使用。</p>
                        <div class="text-start mt-4">
                            <h6>功能说明：</h6>
                            <ul class="list-unstyled">
                                <li>🔑 <strong>API登录:</strong> 使用您的WQB账户登录</li>
                                <li>🔍 <strong>实时搜索:</strong> 获取最新的数据字段信息</li>
                                <li>📍 <strong>Region:</strong> 数据区域 (USA, EUR, CHN等)</li>
                                <li>⏱️ <strong>Delay:</strong> 数据延迟 (0=实时, 1=延迟)</li>
                                <li>🌐 <strong>Universe:</strong> 股票池范围</li>
                                <li>🔤 <strong>关键词:</strong> 在字段描述中搜索</li>
                                <li>📂 <strong>Category:</strong> 数据大类筛选</li>
                                <li>📊 <strong>Type:</strong> 数据类型筛选</li>
                                <li>📈 <strong>Limit:</strong> 每个响应的最大结果数量</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🔑 登录您的WQB账户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>🌟 登录后您可以：</strong>
                            <ul class="mb-0 mt-2">
                                <li>使用实时API搜索获取最新数据</li>
                                <li>访问完整的数据字段信息</li>
                                <li>获得更准确的搜索结果</li>
                            </ul>
                        </div>
                    </div>
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名/邮箱</label>
                            <input type="text" class="form-control" id="username" required
                                   placeholder="请输入您的WQB账户邮箱">
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" required
                                   placeholder="请输入您的WQB账户密码">
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                🔒 您的账户信息仅用于API认证，不会被存储或记录
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="loginSubmitBtn">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 公式编辑器模态框 -->
    <div class="modal fade" id="formulaModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">📝 公式编辑器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>💡 使用说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>在公式中使用 <code>&lt;placeholder_name/&gt;</code> 标记需要填入因子的位置</li>
                                <li>例如: <code>&lt;price_factor/&gt; / &lt;volume_factor/&gt;</code></li>
                                <li>系统会用搜索结果中的因子替换占位符</li>
                                <li>可以选择"前n个"因子批量生成公式</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="formulaTemplate" class="form-label">公式模板</label>
                        <textarea class="form-control" id="formulaTemplate" rows="4"
                                  placeholder="例如: (<price_factor/> + <volume_factor/>) / 2&#10;或者: log(<market_cap/>) * <pe_ratio/>"></textarea>
                        <small class="text-muted">使用 &lt;placeholder_name/&gt; 标记需要填入因子的位置</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="topNFactors" class="form-label">使用前N个因子</label>
                                <input type="number" class="form-control" id="topNFactors" value="5" min="1" max="100">
                                <small class="text-muted">从搜索结果中选择前N个因子</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="placeholderMapping" class="form-label">占位符映射</label>
                                <select class="form-control" id="placeholderMapping">
                                    <option value="sequential">顺序填充</option>
                                    <option value="all_combinations">所有组合</option>
                                </select>
                                <small class="text-muted">如何将因子分配给占位符</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="generateFormulasBtn">
                            🚀 生成公式
                        </button>
                        <button type="button" class="btn btn-secondary" id="previewBtn">
                            👁️ 预览
                        </button>
                    </div>

                    <div id="formulaPreview" class="mb-3" style="display: none;">
                        <label class="form-label">生成的公式预览</label>
                        <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                            <div id="previewContent"></div>
                        </div>
                    </div>

                    <div id="formulaOutput" class="mb-3" style="display: none;">
                        <label for="generatedFormulas" class="form-label">生成的公式</label>
                        <textarea class="form-control" id="generatedFormulas" rows="8" readonly></textarea>
                        <small class="text-muted">公式已用逗号分隔，可以直接复制使用</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" id="copyFormulasBtn" style="display: none;">
                        📋 复制公式
                    </button>
                    <button type="button" class="btn btn-info" id="exportFormulasBtn" style="display: none;">
                        💾 导出文件
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简化的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('searchForm');
            const testBtn = document.getElementById('testBtn');
            const clearBtn = document.getElementById('clearBtn');
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const results = document.getElementById('results');
            const resultCount = document.getElementById('resultCount');
            const apiStatus = document.getElementById('api_status');

            // 登录相关元素
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            const loginForm = document.getElementById('loginForm');
            const loginSubmitBtn = document.getElementById('loginSubmitBtn');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // 公式编辑器相关元素
            const formulaBtn = document.getElementById('formulaBtn');
            const formulaModal = new bootstrap.Modal(document.getElementById('formulaModal'));
            const formulaTemplate = document.getElementById('formulaTemplate');
            const topNFactors = document.getElementById('topNFactors');
            const placeholderMapping = document.getElementById('placeholderMapping');
            const generateFormulasBtn = document.getElementById('generateFormulasBtn');
            const previewBtn = document.getElementById('previewBtn');
            const formulaPreview = document.getElementById('formulaPreview');
            const previewContent = document.getElementById('previewContent');
            const formulaOutput = document.getElementById('formulaOutput');
            const generatedFormulas = document.getElementById('generatedFormulas');
            const copyFormulasBtn = document.getElementById('copyFormulasBtn');
            const exportFormulasBtn = document.getElementById('exportFormulasBtn');

            // 存储当前搜索结果
            let currentSearchResults = [];

            // 页面加载时检查登录状态，如果未登录则显示登录界面
            checkLoginStatus();

            // 搜索表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // 登录按钮
            loginBtn.addEventListener('click', function() {
                loginModal.show();
            });

            // 登录提交
            loginSubmitBtn.addEventListener('click', performLogin);

            // 登录表单回车提交
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                performLogin();
            });

            // 公式编辑器按钮
            formulaBtn.addEventListener('click', function() {
                if (currentSearchResults.length === 0) {
                    alert('请先进行搜索以获取因子数据');
                    return;
                }
                formulaModal.show();
            });

            // 生成公式按钮
            generateFormulasBtn.addEventListener('click', generateFormulas);

            // 预览按钮
            previewBtn.addEventListener('click', previewFormulas);

            // 复制公式按钮
            copyFormulasBtn.addEventListener('click', copyFormulas);

            // 导出公式按钮
            exportFormulasBtn.addEventListener('click', exportFormulas);

            // 测试连接
            testBtn.addEventListener('click', testConnection);

            // 清空结果
            clearBtn.addEventListener('click', function() {
                results.innerHTML = `
                    <div class="welcome-message">
                        <h4>🔍 数据字段检索器</h4>
                        <p>请在左侧设置搜索条件，然后点击搜索按钮开始查找数据字段。</p>
                    </div>
                `;
                resultCount.textContent = '';
                hideError();
            });

            function performSearch() {
                const searchData = {
                    region: document.getElementById('region').value,
                    delay: parseInt(document.getElementById('delay').value),
                    universe: document.getElementById('universe').value,
                    search: document.getElementById('search').value,
                    category: document.getElementById('category').value,
                    type: document.getElementById('type').value,
                    limit: parseInt(document.getElementById('limit').value),
                    use_api: true  // 总是使用API搜索
                };

                console.log('搜索参数:', searchData);
                showLoading();
                hideError();

                // API搜索超时时间
                const timeoutMs = 90000; // 90秒超时

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

                fetch('/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(searchData),
                    signal: controller.signal
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    return response.json();
                })
                .then(data => {
                    console.log('搜索结果:', data);
                    hideLoading();
                    if (data.success) {
                        currentSearchResults = data.results; // 保存搜索结果供公式编辑器使用
                        displayResults(data.results, data.count);
                    } else {
                        showError(data.error || '搜索失败');
                    }
                })
                .catch(err => {
                    clearTimeout(timeoutId);
                    console.error('搜索错误:', err);
                    hideLoading();
                    if (err.name === 'AbortError') {
                        showError(`搜索超时 (${timeoutMs/1000}秒)。API搜索可能需要更长时间，请尝试使用本地搜索或减少结果数量。`);
                    } else {
                        showError('网络错误: ' + err.message);
                    }
                });
            }

            function testConnection() {
                testBtn.innerHTML = '🔄 测试中...';
                testBtn.disabled = true;

                fetch('/test_connection')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`✅ API连接成功！\\n用户ID: ${data.user_id}`);
                    } else {
                        alert(`❌ API连接失败: ${data.error}`);
                    }
                })
                .catch(err => {
                    alert('❌ 连接测试失败: ' + err.message);
                })
                .finally(() => {
                    testBtn.innerHTML = '🔌 测试连接';
                    testBtn.disabled = false;
                });
            }

            function displayResults(resultsData, count) {
                resultCount.textContent = `找到 ${count} 个结果`;

                if (!resultsData || resultsData.length === 0) {
                    results.innerHTML = `
                        <div class="welcome-message">
                            <h4>😔 未找到匹配的结果</h4>
                            <p>请尝试调整搜索条件或使用不同的关键词。</p>
                        </div>
                    `;
                    return;
                }

                let html = '';
                resultsData.forEach(item => {
                    const coverage = item.coverage ? (item.coverage * 100).toFixed(1) + '%' : 'N/A';
                    const categoryName = item['category.name'] || (item.category && item.category.name) || 'N/A';
                    const datasetName = item['dataset.name'] || (item.dataset && item.dataset.name) || 'N/A';

                    html += `
                        <div class="result-item">
                            <div class="d-flex align-items-center mb-2">
                                <span class="result-id">${item.id || 'N/A'}</span>
                                <span class="result-type ${item.type || ''}">${item.type || 'N/A'}</span>
                            </div>
                            <div class="mb-2">${item.description || '无描述'}</div>
                            <div class="result-meta">
                                <span class="category-badge">${categoryName}</span>
                                <span>📊 覆盖率: ${coverage}</span>
                                <span>👥 用户: ${item.userCount || item.user_count || 'N/A'}</span>
                                <span>🔢 Alpha: ${item.alphaCount || item.alpha_count || 'N/A'}</span>
                                <span>💾 数据集: ${datasetName}</span>
                            </div>
                        </div>
                    `;
                });

                results.innerHTML = html;
            }

            function showLoading() {
                // 现在总是使用API搜索
                const loadingText = '<p class="mt-2">API搜索中，请耐心等待（可能需要30-90秒）...</p>';

                loading.innerHTML = `
                    <div class="spinner-border text-primary" role="status"></div>
                    ${loadingText}
                `;
                loading.style.display = 'block';
                results.style.display = 'none';
            }

            function hideLoading() {
                loading.style.display = 'none';
                results.style.display = 'block';
            }

            function showError(message) {
                error.textContent = message;
                error.style.display = 'block';
                setTimeout(() => error.style.display = 'none', 5000);
            }

            function hideError() {
                error.style.display = 'none';
            }

            function checkLoginStatus() {
                fetch('/login_status')
                .then(response => response.json())
                .then(data => {
                    if (data.logged_in) {
                        updateLoginStatus(true, data.user_id);
                    } else {
                        updateLoginStatus(false);
                        // 如果未登录，自动显示登录界面
                        setTimeout(() => {
                            loginModal.show();
                        }, 500); // 延迟500ms显示，让页面先加载完成
                    }
                })
                .catch(err => {
                    console.error('检查登录状态失败:', err);
                    updateLoginStatus(false);
                    // 出错时也显示登录界面
                    setTimeout(() => {
                        loginModal.show();
                    }, 500);
                });
            }

            function updateLoginStatus(loggedIn, userId = null) {
                if (loggedIn) {
                    loginBtn.innerHTML = '👤 已登录';
                    loginBtn.className = 'btn btn-success w-100 mb-2';
                    loginBtn.onclick = logout;
                    testBtn.disabled = false;
                    apiStatus.innerHTML = `<small class="text-success">✅ 已登录用户: ${userId}</small>`;
                } else {
                    loginBtn.innerHTML = '🔑 API登录';
                    loginBtn.className = 'btn btn-warning w-100 mb-2';
                    loginBtn.onclick = () => loginModal.show();
                    testBtn.disabled = true;
                    apiStatus.innerHTML = '<small class="text-warning">⚠️ 需要先登录API账户</small>';
                }
            }

            function performLogin() {
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                if (!username || !password) {
                    alert('请输入用户名和密码');
                    return;
                }

                loginSubmitBtn.innerHTML = '🔄 登录中...';
                loginSubmitBtn.disabled = true;

                fetch('/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loginModal.hide();
                        updateLoginStatus(true, data.user_id);
                        usernameInput.value = '';
                        passwordInput.value = '';
                        alert(`✅ 登录成功！用户ID: ${data.user_id}`);
                    } else {
                        alert(`❌ 登录失败: ${data.error}`);
                    }
                })
                .catch(err => {
                    alert('❌ 登录请求失败: ' + err.message);
                })
                .finally(() => {
                    loginSubmitBtn.innerHTML = '登录';
                    loginSubmitBtn.disabled = false;
                });
            }

            function logout() {
                if (confirm('确定要登出吗？')) {
                    fetch('/logout', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateLoginStatus(false);
                            alert('✅ 已登出');
                        }
                    })
                    .catch(err => {
                        console.error('登出失败:', err);
                    });
                }
            }

            // 公式编辑器功能函数
            function extractPlaceholders(template) {
                const regex = /<([^/>]+)\/>/g;
                const placeholders = [];
                let match;
                while ((match = regex.exec(template)) !== null) {
                    if (!placeholders.includes(match[1])) {
                        placeholders.push(match[1]);
                    }
                }
                return placeholders;
            }

            function previewFormulas() {
                const template = formulaTemplate.value.trim();
                if (!template) {
                    alert('请输入公式模板');
                    return;
                }

                const placeholders = extractPlaceholders(template);
                if (placeholders.length === 0) {
                    alert('公式模板中没有找到占位符。请使用 <placeholder_name/> 格式');
                    return;
                }

                const topN = parseInt(topNFactors.value) || 5;
                const factors = currentSearchResults.slice(0, topN);

                if (factors.length === 0) {
                    alert('没有可用的因子数据');
                    return;
                }

                let previewHtml = `<strong>占位符:</strong> ${placeholders.join(', ')}<br>`;
                previewHtml += `<strong>可用因子 (前${topN}个):</strong><br>`;
                factors.forEach((factor, index) => {
                    previewHtml += `${index + 1}. ${factor.id}<br>`;
                });

                previewContent.innerHTML = previewHtml;
                formulaPreview.style.display = 'block';
            }

            function generateFormulas() {
                const template = formulaTemplate.value.trim();
                if (!template) {
                    alert('请输入公式模板');
                    return;
                }

                const placeholders = extractPlaceholders(template);
                if (placeholders.length === 0) {
                    alert('公式模板中没有找到占位符。请使用 <placeholder_name/> 格式');
                    return;
                }

                const topN = parseInt(topNFactors.value) || 5;
                const factors = currentSearchResults.slice(0, topN);
                const mappingType = placeholderMapping.value;

                if (factors.length === 0) {
                    alert('没有可用的因子数据');
                    return;
                }

                let formulas = [];

                if (mappingType === 'sequential') {
                    // 顺序填充：每个公式使用连续的因子
                    for (let i = 0; i < factors.length; i++) {
                        let formula = template;
                        placeholders.forEach((placeholder, index) => {
                            const factorIndex = (i + index) % factors.length;
                            const factorId = factors[factorIndex].id;
                            formula = formula.replace(new RegExp(`<${placeholder}/>`, 'g'), factorId);
                        });
                        formulas.push(formula);
                    }
                } else if (mappingType === 'all_combinations') {
                    // 所有组合：生成所有可能的因子组合
                    if (placeholders.length === 1) {
                        // 单个占位符，直接替换
                        factors.forEach(factor => {
                            let formula = template.replace(new RegExp(`<${placeholders[0]}/>`, 'g'), factor.id);
                            formulas.push(formula);
                        });
                    } else if (placeholders.length === 2) {
                        // 两个占位符，生成所有组合
                        factors.forEach(factor1 => {
                            factors.forEach(factor2 => {
                                if (factor1.id !== factor2.id) { // 避免相同因子
                                    let formula = template;
                                    formula = formula.replace(new RegExp(`<${placeholders[0]}/>`, 'g'), factor1.id);
                                    formula = formula.replace(new RegExp(`<${placeholders[1]}/>`, 'g'), factor2.id);
                                    formulas.push(formula);
                                }
                            });
                        });
                    } else {
                        // 多个占位符，使用递归生成组合（限制数量避免过多）
                        const maxCombinations = 50;
                        const combinations = generateCombinations(factors, placeholders.length, maxCombinations);
                        combinations.forEach(combination => {
                            let formula = template;
                            placeholders.forEach((placeholder, index) => {
                                formula = formula.replace(new RegExp(`<${placeholder}/>`, 'g'), combination[index].id);
                            });
                            formulas.push(formula);
                        });
                    }
                }

                // 显示结果
                const formulaText = formulas.join(',\n');
                generatedFormulas.value = formulaText;
                formulaOutput.style.display = 'block';
                copyFormulasBtn.style.display = 'inline-block';
                exportFormulasBtn.style.display = 'inline-block';

                // 更新预览
                previewContent.innerHTML = `<strong>生成了 ${formulas.length} 个公式</strong><br>` +
                    formulas.slice(0, 5).map((f, i) => `${i + 1}. ${f}`).join('<br>') +
                    (formulas.length > 5 ? '<br>...' : '');
                formulaPreview.style.display = 'block';
            }

            function generateCombinations(factors, length, maxCount) {
                const combinations = [];

                function backtrack(current) {
                    if (combinations.length >= maxCount) return;
                    if (current.length === length) {
                        combinations.push([...current]);
                        return;
                    }

                    for (let factor of factors) {
                        if (!current.some(f => f.id === factor.id)) {
                            current.push(factor);
                            backtrack(current);
                            current.pop();
                        }
                    }
                }

                backtrack([]);
                return combinations;
            }

            function copyFormulas() {
                generatedFormulas.select();
                document.execCommand('copy');
                alert('✅ 公式已复制到剪贴板');
            }

            function exportFormulas() {
                const formulas = generatedFormulas.value;
                if (!formulas) {
                    alert('没有可导出的公式');
                    return;
                }

                const blob = new Blob([formulas], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'generated_formulas.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                alert('✅ 公式已导出到文件');
            }
        });
    </script>
</body>
</html>
