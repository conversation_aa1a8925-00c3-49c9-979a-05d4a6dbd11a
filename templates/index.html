<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字段检索器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { background-color: #343a40; color: white; min-height: 100vh; padding: 20px; }
        .sidebar h4 { color: #17a2b8; margin-bottom: 30px; text-align: center; }
        .sidebar .form-label { color: #adb5bd; font-weight: 500; }
        .sidebar .form-control, .sidebar .form-select {
            background-color: #495057; border: 1px solid #6c757d; color: white;
        }
        .sidebar .form-control:focus, .sidebar .form-select:focus {
            background-color: #495057; border-color: #17a2b8; color: white;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        .sidebar .form-control::placeholder { color: #adb5bd; }
        .sidebar .form-check-label { color: #adb5bd; }
        .main-content { padding: 20px; }
        .content-header {
            background: white; padding: 20px; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;
        }
        .results-container {
            background: white; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-height: 400px; padding: 20px;
        }
        .result-item {
            border-bottom: 1px solid #e9ecef; padding: 15px 0;
        }
        .result-item:last-child { border-bottom: none; }
        .result-id { font-family: monospace; font-weight: bold; color: #007bff; }
        .result-type {
            background-color: #6c757d; color: white; padding: 2px 8px;
            border-radius: 12px; font-size: 0.8rem; margin-left: 10px;
        }
        .result-type.MATRIX { background-color: #28a745; }
        .result-type.VECTOR { background-color: #17a2b8; }
        .result-type.GROUP { background-color: #ffc107; color: #212529; }
        .result-type.UNIVERSE { background-color: #dc3545; }
        .result-meta {
            display: flex; flex-wrap: wrap; gap: 15px;
            font-size: 0.9rem; color: #6c757d; margin-top: 10px;
        }
        .category-badge {
            background-color: #e9ecef; color: #495057;
            padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;
        }
        .welcome-message { text-align: center; color: #6c757d; padding: 40px; }
        .btn-primary { background-color: #17a2b8; border-color: #17a2b8; }
        .btn-primary:hover { background-color: #138496; border-color: #117a8b; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <h4><i class="fas fa-search"></i> 数据字段检索器</h4>

                <form id="searchForm">
                    <div class="mb-3">
                        <label for="region" class="form-label">Region</label>
                        <select class="form-select" id="region">
                            {% for region in regions %}
                            <option value="{{ region }}" {% if region == 'USA' %}selected{% endif %}>{{ region }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="delay" class="form-label">Delay</label>
                        <select class="form-select" id="delay">
                            {% for delay in delays %}
                            <option value="{{ delay }}" {% if delay == 1 %}selected{% endif %}>{{ delay }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="universe" class="form-label">Universe</label>
                        <select class="form-select" id="universe">
                            {% for universe in universes %}
                            <option value="{{ universe }}" {% if universe == 'TOP3000' %}selected{% endif %}>{{ universe }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="search" class="form-label">🔤 搜索关键词</label>
                        <input type="text" class="form-control" id="search"
                               placeholder="例如: price, volume, market cap 或 价格, 成交量, 市值">
                        <small class="text-muted">💡 支持中英文搜索，中文会自动翻译为英文进行搜索</small>
                    </div>

                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category">
                            <option value="">全部</option>
                            {% for cat in categories %}
                            <option value="{{ cat.id }}">{{ cat.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type">
                            <option value="">全部</option>
                            {% for type in types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="limit" class="form-label">返回结果数量</label>
                        <input type="number" class="form-control" id="limit" value="100" min="1" max="5000">
                        <small class="text-muted">最多返回多少个结果 (1-5000，系统会自动收集足够的API响应)</small>
                    </div>

                    <div class="mb-3">
                        <div id="api_status" class="mt-2">
                            <small class="text-warning">⚠️ 需要先登录API账户</small>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        🔍 搜索
                    </button>

                    <!-- 公式编辑器按钮 -->
                    <button type="button" class="btn btn-success w-100 mt-2" id="formulaBtn">
                        📝 公式编辑器
                    </button>

                    <button type="button" class="btn btn-warning w-100 mb-2" id="loginBtn">
                        🔑 API登录
                    </button>

                    <button type="button" class="btn btn-info w-100 mb-2" id="testBtn" disabled>
                        🔌 测试连接
                    </button>

                    <button type="button" class="btn btn-success w-100" id="clearBtn">
                        🗑️ 清空结果
                    </button>
                </form>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>搜索结果</h2>
                            <div id="resultCount" class="text-muted"></div>
                        </div>
                        <!-- 翻译和快速赋值控件 -->
                        <div class="d-flex align-items-center gap-3">
                            <!-- 翻译控件 -->
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enableTranslation">
                                <label class="form-check-label" for="enableTranslation">
                                    <small>🌐 中文翻译</small>
                                </label>
                            </div>
                            <button class="btn btn-sm btn-outline-info" id="translateResultsBtn" style="display: none;">
                                🔤 翻译描述
                            </button>

                            <!-- 快速赋值控件 -->
                            <div class="d-flex align-items-center gap-2" id="quickAssignControls" style="display: none;">
                                <small class="text-muted">快速赋值:</small>
                                <select class="form-select form-select-sm" id="quickTargetPlaceholder" style="width: 150px;">
                                    <option value="">选择占位符</option>
                                </select>
                                <select class="form-select form-select-sm" id="quickTopN" style="width: 100px;">
                                    <option value="1">前1个</option>
                                    <option value="3" selected>前3个</option>
                                    <option value="5">前5个</option>
                                    <option value="10">前10个</option>
                                    <option value="all">全部</option>
                                </select>
                                <button class="btn btn-sm btn-success" id="quickAssignBtn">✅ 赋值</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="loading" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">搜索中...</p>
                </div>

                <div id="error" class="alert alert-danger" style="display: none;"></div>

                <div id="results" class="results-container">
                    <div class="welcome-message">
                        <h4>🔍 WQB数据字段检索器</h4>
                        <p>欢迎使用WQB数据字段检索器！请先登录您的API账户开始使用。</p>
                        <div class="text-start mt-4">
                            <h6>功能说明：</h6>
                            <ul class="list-unstyled">
                                <li>🔑 <strong>API登录:</strong> 使用您的WQB账户登录</li>
                                <li>🔍 <strong>实时搜索:</strong> 获取最新的数据字段信息</li>
                                <li>📍 <strong>Region:</strong> 数据区域 (USA, EUR, CHN等)</li>
                                <li>⏱️ <strong>Delay:</strong> 数据延迟 (0=实时, 1=延迟)</li>
                                <li>🌐 <strong>Universe:</strong> 股票池范围</li>
                                <li>🔤 <strong>关键词:</strong> 在字段描述中搜索</li>
                                <li>📂 <strong>Category:</strong> 数据大类筛选</li>
                                <li>📊 <strong>Type:</strong> 数据类型筛选</li>
                                <li>📈 <strong>Limit:</strong> 每个响应的最大结果数量</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🔑 登录您的WQB账户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>🌟 登录后您可以：</strong>
                            <ul class="mb-0 mt-2">
                                <li>使用实时API搜索获取最新数据</li>
                                <li>访问完整的数据字段信息</li>
                                <li>获得更准确的搜索结果</li>
                            </ul>
                        </div>
                    </div>
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名/邮箱</label>
                            <input type="text" class="form-control" id="username" required
                                   placeholder="请输入您的WQB账户邮箱">
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" required
                                   placeholder="请输入您的WQB账户密码">
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                🔒 您的账户信息仅用于API认证，不会被存储或记录
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="loginSubmitBtn">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 公式编辑器模态框 -->
    <div class="modal fade" id="formulaModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">📝 公式编辑器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>💡 使用说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>在公式中使用 <code>&lt;placeholder_name/&gt;</code> 标记需要填入因子的位置</li>
                                <li>例如: <code>&lt;price_factor/&gt; / &lt;volume_factor/&gt;</code></li>
                                <li>点击"🔍 解析模板"后，为每个占位符<strong>单独搜索</strong>相关因子</li>
                                <li>每个占位符可以用不同的关键词搜索，获得精确匹配的因子</li>
                                <li>系统会生成所有可能的因子组合公式</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="formulaTemplate" class="form-label">公式模板</label>
                        <textarea class="form-control" id="formulaTemplate" rows="4"
                                  placeholder="例如: (<price_factor/> + <volume_factor/>) / 2&#10;或者: log(<market_cap/>) * <pe_ratio/>"></textarea>
                        <small class="text-muted">使用 &lt;placeholder_name/&gt; 标记需要填入因子的位置</small>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="formulaTemplate" class="form-label">公式模板</label>
                                <textarea class="form-control" id="formulaTemplate" rows="4"
                                          placeholder="例如: <price/> / <volume/>&#10;或者: (<high/> + <low/> + <close/>) / 3&#10;或者: log(<market_cap/>) * <pe_ratio/>"></textarea>
                                <small class="text-muted">使用 &lt;placeholder_name/&gt; 标记需要填入因子的位置</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <small class="text-muted">📋 占位符列表</small>
                                </div>
                                <div class="card-body p-2">
                                    <div id="placeholderTags" class="mb-2">
                                        <small class="text-muted">在模板中输入 &lt;name/&gt; 会自动显示在这里</small>
                                    </div>
                                    <small class="text-muted">💡 写过的占位符会被记住，可以在搜索结果页面快速赋值</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="generateFormulasBtn">
                            🚀 生成公式
                        </button>
                        <button type="button" class="btn btn-secondary" id="previewBtn">
                            👁️ 预览
                        </button>
                        <small class="text-muted d-block mt-2">
                            💡 提示：先在搜索页面为占位符赋值因子，然后回来生成公式
                        </small>
                    </div>

                    <div id="formulaPreview" class="mb-3" style="display: none;">
                        <label class="form-label">生成的公式预览</label>
                        <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                            <div id="previewContent"></div>
                        </div>
                    </div>

                    <div id="formulaOutput" class="mb-3" style="display: none;">
                        <label for="generatedFormulas" class="form-label">生成的公式</label>
                        <textarea class="form-control" id="generatedFormulas" rows="8" readonly></textarea>
                        <small class="text-muted">公式已用逗号分隔，可以直接复制使用</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" id="copyFormulasBtn" style="display: none;">
                        📋 复制公式
                    </button>
                    <button type="button" class="btn btn-info" id="exportFormulasBtn" style="display: none;">
                        💾 导出文件
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简化的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('searchForm');
            const testBtn = document.getElementById('testBtn');
            const clearBtn = document.getElementById('clearBtn');
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const results = document.getElementById('results');
            const resultCount = document.getElementById('resultCount');
            const apiStatus = document.getElementById('api_status');

            // 登录相关元素
            const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
            const loginForm = document.getElementById('loginForm');
            const loginSubmitBtn = document.getElementById('loginSubmitBtn');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // 公式编辑器相关元素
            const formulaBtn = document.getElementById('formulaBtn');
            const formulaModal = new bootstrap.Modal(document.getElementById('formulaModal'));
            const formulaTemplate = document.getElementById('formulaTemplate');
            const placeholderTags = document.getElementById('placeholderTags');
            const generateFormulasBtn = document.getElementById('generateFormulasBtn');
            const previewBtn = document.getElementById('previewBtn');

            // 快速赋值控件
            const quickAssignControls = document.getElementById('quickAssignControls');
            const quickTargetPlaceholder = document.getElementById('quickTargetPlaceholder');
            const quickTopN = document.getElementById('quickTopN');
            const quickAssignBtn = document.getElementById('quickAssignBtn');

            // 翻译控件
            const enableTranslation = document.getElementById('enableTranslation');
            const translateResultsBtn = document.getElementById('translateResultsBtn');

            // 存储占位符和对应的因子
            let placeholderFactors = {};

            // 存储所有见过的占位符（自动记住）
            let allKnownPlaceholders = new Set();
            const formulaPreview = document.getElementById('formulaPreview');
            const previewContent = document.getElementById('previewContent');
            const formulaOutput = document.getElementById('formulaOutput');
            const generatedFormulas = document.getElementById('generatedFormulas');
            const copyFormulasBtn = document.getElementById('copyFormulasBtn');
            const exportFormulasBtn = document.getElementById('exportFormulasBtn');

            // 存储当前搜索结果
            let currentSearchResults = [];

            // 初始化占位符列表
            updatePlaceholderList();

            // 页面加载时检查登录状态，如果未登录则显示登录界面
            checkLoginStatus();

            // 搜索表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // 登录按钮
            loginBtn.addEventListener('click', function() {
                loginModal.show();
            });

            // 登录提交
            loginSubmitBtn.addEventListener('click', performLogin);

            // 登录表单回车提交
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                performLogin();
            });

            // 公式编辑器按钮 - 可以直接使用
            formulaBtn.addEventListener('click', function() {
                formulaModal.show();
            });

            // 模板输入实时监听
            formulaTemplate.addEventListener('input', updatePlaceholderList);

            // 快速赋值按钮
            quickAssignBtn.addEventListener('click', quickAssignFactors);

            // 翻译开关
            enableTranslation.addEventListener('change', function() {
                if (this.checked && currentSearchResults.length > 0) {
                    translateResultsBtn.style.display = 'inline-block';
                } else {
                    translateResultsBtn.style.display = 'none';
                }
            });

            // 翻译按钮
            translateResultsBtn.addEventListener('click', translateCurrentResults);

            // 生成公式按钮
            generateFormulasBtn.addEventListener('click', generateFormulas);

            // 预览按钮
            previewBtn.addEventListener('click', previewFormulas);

            // 复制公式按钮
            copyFormulasBtn.addEventListener('click', copyFormulas);

            // 导出公式按钮
            exportFormulasBtn.addEventListener('click', exportFormulas);

            // 测试连接
            testBtn.addEventListener('click', testConnection);

            // 清空结果
            clearBtn.addEventListener('click', function() {
                results.innerHTML = `
                    <div class="welcome-message">
                        <h4>🔍 数据字段检索器</h4>
                        <p>请在左侧设置搜索条件，然后点击搜索按钮开始查找数据字段。</p>
                    </div>
                `;
                resultCount.textContent = '';
                hideError();
            });

            function performSearch() {
                const searchData = {
                    region: document.getElementById('region').value,
                    delay: parseInt(document.getElementById('delay').value),
                    universe: document.getElementById('universe').value,
                    search: document.getElementById('search').value,
                    category: document.getElementById('category').value,
                    type: document.getElementById('type').value,
                    limit: parseInt(document.getElementById('limit').value),
                    use_api: true,  // 总是使用API搜索
                    translate_results: enableTranslation.checked  // 添加翻译选项
                };

                console.log('搜索参数:', searchData);
                showLoading();
                hideError();

                // API搜索超时时间
                const timeoutMs = 90000; // 90秒超时

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

                fetch('/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(searchData),
                    signal: controller.signal
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    return response.json();
                })
                .then(data => {
                    console.log('搜索结果:', data);
                    hideLoading();
                    if (data.success) {
                        currentSearchResults = data.results; // 保存搜索结果供公式编辑器使用
                        displayResults(data.results, data.count);

                        // 显示快速赋值控件（如果有占位符的话）
                        if (allKnownPlaceholders.size > 0 && data.results.length > 0) {
                            quickAssignControls.style.display = 'flex';
                        }

                        // 显示翻译按钮（如果开启翻译且有结果）
                        if (enableTranslation.checked && data.results.length > 0) {
                            translateResultsBtn.style.display = 'inline-block';
                        }
                    } else {
                        showError(data.error || '搜索失败');
                    }
                })
                .catch(err => {
                    clearTimeout(timeoutId);
                    console.error('搜索错误:', err);
                    hideLoading();
                    if (err.name === 'AbortError') {
                        showError(`搜索超时 (${timeoutMs/1000}秒)。API搜索可能需要更长时间，请尝试使用本地搜索或减少结果数量。`);
                    } else {
                        showError('网络错误: ' + err.message);
                    }
                });
            }

            function testConnection() {
                testBtn.innerHTML = '🔄 测试中...';
                testBtn.disabled = true;

                fetch('/test_connection')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`✅ API连接成功！\\n用户ID: ${data.user_id}`);
                    } else {
                        alert(`❌ API连接失败: ${data.error}`);
                    }
                })
                .catch(err => {
                    alert('❌ 连接测试失败: ' + err.message);
                })
                .finally(() => {
                    testBtn.innerHTML = '🔌 测试连接';
                    testBtn.disabled = false;
                });
            }

            function displayResults(resultsData, count) {
                resultCount.textContent = `找到 ${count} 个结果`;

                if (!resultsData || resultsData.length === 0) {
                    results.innerHTML = `
                        <div class="welcome-message">
                            <h4>😔 未找到匹配的结果</h4>
                            <p>请尝试调整搜索条件或使用不同的关键词。</p>
                        </div>
                    `;
                    return;
                }

                let html = '';
                resultsData.forEach(item => {
                    const coverage = item.coverage ? (item.coverage * 100).toFixed(1) + '%' : 'N/A';
                    const categoryName = item['category.name'] || (item.category && item.category.name) || 'N/A';
                    const datasetName = item['dataset.name'] || (item.dataset && item.dataset.name) || 'N/A';

                    html += `
                        <div class="result-item">
                            <div class="d-flex align-items-center mb-2">
                                <span class="result-id">${item.id || 'N/A'}</span>
                                <span class="result-type ${item.type || ''}">${item.type || 'N/A'}</span>
                            </div>
                            <div class="mb-2">
                                ${item.description_translated ?
                                    `<div class="mb-1">${item.description_translated}</div>
                                     <small class="text-muted">${item.description_original || item.description}</small>`
                                    : (item.description || '无描述')
                                }
                            </div>
                            <div class="result-meta">
                                <span class="category-badge">${categoryName}</span>
                                <span>📊 覆盖率: ${coverage}</span>
                                <span>👥 用户: ${item.userCount || item.user_count || 'N/A'}</span>
                                <span>🔢 Alpha: ${item.alphaCount || item.alpha_count || 'N/A'}</span>
                                <span>💾 数据集: ${datasetName}</span>
                            </div>
                        </div>
                    `;
                });

                results.innerHTML = html;
            }

            function translateCurrentResults() {
                if (!currentSearchResults || currentSearchResults.length === 0) {
                    alert('没有搜索结果可以翻译');
                    return;
                }

                // 显示翻译进度
                translateResultsBtn.textContent = '🔄 翻译中...';
                translateResultsBtn.disabled = true;

                // 发送翻译请求
                fetch('/translate_results', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ results: currentSearchResults })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新当前搜索结果
                        currentSearchResults = data.translated_results;

                        // 重新显示结果
                        displayResults(currentSearchResults, currentSearchResults.length);

                        // 恢复按钮状态
                        translateResultsBtn.textContent = '✅ 翻译完成';
                        setTimeout(() => {
                            translateResultsBtn.textContent = '🔤 翻译描述';
                            translateResultsBtn.disabled = false;
                        }, 2000);
                    } else {
                        alert('翻译失败: ' + (data.error || '未知错误'));
                        translateResultsBtn.textContent = '🔤 翻译描述';
                        translateResultsBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('翻译请求失败:', error);
                    alert('翻译请求失败，请重试');
                    translateResultsBtn.textContent = '🔤 翻译描述';
                    translateResultsBtn.disabled = false;
                });
            }

            function showLoading() {
                // 现在总是使用API搜索
                const loadingText = '<p class="mt-2">API搜索中，请耐心等待（可能需要30-90秒）...</p>';

                loading.innerHTML = `
                    <div class="spinner-border text-primary" role="status"></div>
                    ${loadingText}
                `;
                loading.style.display = 'block';
                results.style.display = 'none';
            }

            function hideLoading() {
                loading.style.display = 'none';
                results.style.display = 'block';
            }

            function showError(message) {
                error.textContent = message;
                error.style.display = 'block';
                setTimeout(() => error.style.display = 'none', 5000);
            }

            function hideError() {
                error.style.display = 'none';
            }

            function checkLoginStatus() {
                fetch('/login_status')
                .then(response => response.json())
                .then(data => {
                    if (data.logged_in) {
                        updateLoginStatus(true, data.user_id);
                    } else {
                        updateLoginStatus(false);
                        // 如果未登录，自动显示登录界面
                        setTimeout(() => {
                            loginModal.show();
                        }, 500); // 延迟500ms显示，让页面先加载完成
                    }
                })
                .catch(err => {
                    console.error('检查登录状态失败:', err);
                    updateLoginStatus(false);
                    // 出错时也显示登录界面
                    setTimeout(() => {
                        loginModal.show();
                    }, 500);
                });
            }

            function updateLoginStatus(loggedIn, userId = null) {
                if (loggedIn) {
                    loginBtn.innerHTML = '👤 已登录';
                    loginBtn.className = 'btn btn-success w-100 mb-2';
                    loginBtn.onclick = logout;
                    testBtn.disabled = false;
                    apiStatus.innerHTML = `<small class="text-success">✅ 已登录用户: ${userId}</small>`;
                } else {
                    loginBtn.innerHTML = '🔑 API登录';
                    loginBtn.className = 'btn btn-warning w-100 mb-2';
                    loginBtn.onclick = () => loginModal.show();
                    testBtn.disabled = true;
                    apiStatus.innerHTML = '<small class="text-warning">⚠️ 需要先登录API账户</small>';
                }
            }

            function performLogin() {
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                if (!username || !password) {
                    alert('请输入用户名和密码');
                    return;
                }

                loginSubmitBtn.innerHTML = '🔄 登录中...';
                loginSubmitBtn.disabled = true;

                fetch('/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loginModal.hide();
                        updateLoginStatus(true, data.user_id);
                        usernameInput.value = '';
                        passwordInput.value = '';
                        alert(`✅ 登录成功！用户ID: ${data.user_id}`);
                    } else {
                        alert(`❌ 登录失败: ${data.error}`);
                    }
                })
                .catch(err => {
                    alert('❌ 登录请求失败: ' + err.message);
                })
                .finally(() => {
                    loginSubmitBtn.innerHTML = '登录';
                    loginSubmitBtn.disabled = false;
                });
            }

            function logout() {
                if (confirm('确定要登出吗？')) {
                    fetch('/logout', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateLoginStatus(false);
                            alert('✅ 已登出');
                        }
                    })
                    .catch(err => {
                        console.error('登出失败:', err);
                    });
                }
            }

            // 公式编辑器功能函数
            function extractPlaceholders(template) {
                const regex = /<([^/>]+)\/>/g;
                const placeholders = [];
                let match;
                while ((match = regex.exec(template)) !== null) {
                    if (!placeholders.includes(match[1])) {
                        placeholders.push(match[1]);
                    }
                }
                return placeholders;
            }

            function updatePlaceholderList() {
                const template = formulaTemplate.value.trim();
                const currentPlaceholders = extractPlaceholders(template);

                // 将当前占位符添加到已知占位符集合中（自动记住）
                currentPlaceholders.forEach(placeholder => {
                    allKnownPlaceholders.add(placeholder);
                });

                // 更新占位符标签显示（显示当前模板中的占位符）
                if (currentPlaceholders.length === 0) {
                    placeholderTags.innerHTML = '<small class="text-muted">在模板中输入 &lt;name/&gt; 会自动显示在这里</small>';
                } else {
                    // 显示占位符标签
                    let tagsHtml = '';
                    currentPlaceholders.forEach(placeholder => {
                        const hasFactors = placeholderFactors[placeholder] && placeholderFactors[placeholder].length > 0;
                        const badgeClass = hasFactors ? 'bg-success' : 'bg-secondary';
                        const count = hasFactors ? placeholderFactors[placeholder].length : 0;
                        tagsHtml += `<span class="badge ${badgeClass} me-1 mb-1">&lt;${placeholder}/&gt; (${count})</span>`;
                    });
                    placeholderTags.innerHTML = tagsHtml;
                }

                // 更新快速赋值选择框（包含所有见过的占位符）
                updateQuickAssignSelect();
            }

            function updateQuickAssignSelect() {
                // 更新快速赋值选择框
                let optionsHtml = '<option value="">选择占位符</option>';
                Array.from(allKnownPlaceholders).sort().forEach(placeholder => {
                    const hasFactors = placeholderFactors[placeholder] && placeholderFactors[placeholder].length > 0;
                    const count = hasFactors ? ` (${placeholderFactors[placeholder].length})` : ' (0)';
                    optionsHtml += `<option value="${placeholder}">&lt;${placeholder}/&gt;${count}</option>`;
                });

                if (quickTargetPlaceholder) quickTargetPlaceholder.innerHTML = optionsHtml;
            }



            function quickAssignFactors() {
                const selectedPlaceholder = quickTargetPlaceholder.value;
                const topN = quickTopN.value;

                if (!selectedPlaceholder) {
                    alert('请选择要赋值的占位符');
                    return;
                }

                if (!currentSearchResults || currentSearchResults.length === 0) {
                    alert('没有搜索结果可以赋值');
                    return;
                }

                // 获取要赋值的因子
                let factorsToAssign;
                if (topN === 'all') {
                    factorsToAssign = [...currentSearchResults];
                } else {
                    const n = parseInt(topN);
                    factorsToAssign = currentSearchResults.slice(0, n);
                }

                // 赋值给占位符
                placeholderFactors[selectedPlaceholder] = factorsToAssign;

                // 更新显示
                updatePlaceholderList();
                updateQuickAssignSelect();

                // 显示成功消息
                const factorNames = factorsToAssign.map(f => f.id).join(', ');

                // 创建一个更友好的提示
                const message = `✅ 赋值成功！\n\n占位符: <${selectedPlaceholder}/>\n数量: ${factorsToAssign.length} 个因子\n因子: ${factorNames.length > 100 ? factorNames.substring(0, 100) + '...' : factorNames}`;
                alert(message);

                console.log(`快速赋值完成: <${selectedPlaceholder}/> = [${factorNames}]`);
            }



            function setupQuickSelectEvents() {
                // 移除之前的事件监听器（如果存在）
                placeholderList.removeEventListener('click', handleQuickSelectClick);

                // 添加事件委托
                placeholderList.addEventListener('click', handleQuickSelectClick);
                console.log('快速选择事件监听器已设置');
            }

            function handleQuickSelectClick(event) {
                const button = event.target;
                if (!button.matches('button[data-action]')) {
                    return; // 不是快速选择按钮
                }

                const action = button.getAttribute('data-action');
                const index = button.getAttribute('data-index');
                const n = button.getAttribute('data-n');

                console.log(`快速选择按钮被点击: action=${action}, index=${index}, n=${n}`);

                switch (action) {
                    case 'selectTopN':
                        selectTopN(index, parseInt(n));
                        break;
                    case 'selectAll':
                        selectAll(index);
                        break;
                    case 'clearSelection':
                        clearSelection(index);
                        break;
                }
            }

            // 为占位符单独搜索的功能
            window.searchForPlaceholder = function(placeholderIndex) {
                const searchInput = document.getElementById(`search_${placeholderIndex}`);
                const searchTerm = searchInput.value.trim();

                if (!searchTerm) {
                    alert('请输入搜索关键词');
                    return;
                }

                console.log(`为占位符 ${placeholderIndex} 搜索: ${searchTerm}`);

                // 显示加载状态
                const factorsSelect = document.getElementById(`factors_${placeholderIndex}`);
                const countBadge = document.getElementById(`count_${placeholderIndex}`);

                factorsSelect.innerHTML = '<option disabled>🔍 搜索中...</option>';
                countBadge.textContent = '...';
                countBadge.className = 'badge bg-warning';

                // 构建搜索参数
                const searchData = {
                    region: document.getElementById('region').value,
                    delay: parseInt(document.getElementById('delay').value),
                    universe: document.getElementById('universe').value,
                    search: searchTerm,
                    category: document.getElementById('category').value,
                    type: document.getElementById('type').value,
                    limit: 50, // 为每个占位符搜索50个结果
                    use_api: true
                };

                // 发送搜索请求
                fetch('/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(searchData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const results = data.results || [];

                        // 更新选择框
                        factorsSelect.innerHTML = results.map((factor, index) =>
                            `<option value="${index}">${factor.id} - ${factor.description?.substring(0, 50) || 'No description'}...</option>`
                        ).join('');

                        // 更新计数
                        countBadge.textContent = results.length;
                        countBadge.className = results.length > 0 ? 'badge bg-success' : 'badge bg-secondary';

                        // 存储搜索结果
                        if (!window.placeholderSearchResults) {
                            window.placeholderSearchResults = {};
                        }
                        window.placeholderSearchResults[placeholderIndex] = results;

                        console.log(`占位符 ${placeholderIndex} 搜索完成: ${results.length} 个结果`);

                        if (results.length === 0) {
                            factorsSelect.innerHTML = '<option disabled>没有找到相关因子，请尝试其他关键词</option>';
                        }
                    } else {
                        factorsSelect.innerHTML = '<option disabled>搜索失败，请重试</option>';
                        countBadge.textContent = '!';
                        countBadge.className = 'badge bg-danger';
                        console.error('搜索失败:', data.error);
                    }
                })
                .catch(error => {
                    factorsSelect.innerHTML = '<option disabled>网络错误，请重试</option>';
                    countBadge.textContent = '!';
                    countBadge.className = 'badge bg-danger';
                    console.error('搜索请求失败:', error);
                });
            };

            // 快速选择函数
            window.selectTopN = function(placeholderIndex, n) {
                const select = document.getElementById(`factors_${placeholderIndex}`);
                if (!select) {
                    console.error(`找不到选择框: factors_${placeholderIndex}`);
                    return;
                }
                // 清空当前选择
                for (let option of select.options) {
                    option.selected = false;
                }
                // 选择前n个
                for (let i = 0; i < Math.min(n, select.options.length); i++) {
                    select.options[i].selected = true;
                }
                console.log(`已选择前${n}个因子`);
            };

            window.selectAll = function(placeholderIndex) {
                const select = document.getElementById(`factors_${placeholderIndex}`);
                if (!select) {
                    console.error(`找不到选择框: factors_${placeholderIndex}`);
                    return;
                }
                for (let option of select.options) {
                    option.selected = true;
                }
                console.log(`已全选所有因子`);
            };

            window.clearSelection = function(placeholderIndex) {
                const select = document.getElementById(`factors_${placeholderIndex}`);
                if (!select) {
                    console.error(`找不到选择框: factors_${placeholderIndex}`);
                    return;
                }
                for (let option of select.options) {
                    option.selected = false;
                }
                console.log(`已清空选择`);
            };

            function previewFormulas() {
                const template = formulaTemplate.value.trim();
                if (!template) {
                    alert('请输入公式模板');
                    return;
                }

                const placeholders = extractPlaceholders(template);
                if (placeholders.length === 0) {
                    alert('公式模板中没有找到占位符');
                    return;
                }

                // 检查所有占位符是否都有赋值
                const missingPlaceholders = [];
                const validPlaceholders = [];

                placeholders.forEach(placeholder => {
                    if (!placeholderFactors[placeholder] || placeholderFactors[placeholder].length === 0) {
                        missingPlaceholders.push(placeholder);
                    } else {
                        validPlaceholders.push(placeholder);
                    }
                });

                let previewHtml = '<strong>配置预览:</strong><br>';

                // 显示已配置的占位符
                validPlaceholders.forEach(placeholder => {
                    const factors = placeholderFactors[placeholder];
                    previewHtml += `<strong>&lt;${placeholder}/&gt;</strong>: ${factors.length} 个因子<br>`;
                    previewHtml += `   → ${factors.map(f => f.id).join(', ')}<br>`;
                });

                // 显示未配置的占位符
                if (missingPlaceholders.length > 0) {
                    previewHtml += `<br><span class="text-warning">⚠️ 未配置的占位符: ${missingPlaceholders.map(p => `<${p}/>`).join(', ')}</span><br>`;
                    previewHtml += `<small class="text-muted">请先搜索因子，然后赋值给这些占位符</small>`;
                } else {
                    // 计算将生成的公式数量
                    const totalCombinations = validPlaceholders.reduce((total, placeholder) => {
                        return total * placeholderFactors[placeholder].length;
                    }, 1);
                    previewHtml += `<br><strong>将生成 ${totalCombinations} 个公式</strong>`;

                    if (totalCombinations > 100) {
                        previewHtml += `<br><span class="text-warning">⚠️ 公式数量较多，生成可能需要一些时间</span>`;
                    }
                }

                previewContent.innerHTML = previewHtml;
                formulaPreview.style.display = 'block';
            }

            function getSelectedFactors() {
                if (!window.currentPlaceholders) {
                    alert('请先解析模板');
                    return null;
                }

                const placeholderFactors = [];

                for (let i = 0; i < window.currentPlaceholders.length; i++) {
                    const select = document.getElementById(`factors_${i}`);
                    const selectedFactors = [];

                    // 检查是否有搜索结果
                    if (!window.placeholderSearchResults || !window.placeholderSearchResults[i]) {
                        alert(`请先为占位符 <${window.currentPlaceholders[i]}/> 搜索因子`);
                        return null;
                    }

                    const searchResults = window.placeholderSearchResults[i];

                    for (let option of select.selectedOptions) {
                        const factorIndex = parseInt(option.value);
                        if (searchResults[factorIndex]) {
                            selectedFactors.push(searchResults[factorIndex]);
                        }
                    }

                    if (selectedFactors.length === 0) {
                        alert(`请为占位符 <${window.currentPlaceholders[i]}/> 选择至少一个因子`);
                        return null;
                    }

                    placeholderFactors.push(selectedFactors);
                }

                return placeholderFactors;
            }

            function generateFormulas() {
                const template = formulaTemplate.value.trim();
                if (!template) {
                    alert('请输入公式模板');
                    return;
                }

                const placeholders = extractPlaceholders(template);
                if (placeholders.length === 0) {
                    alert('公式模板中没有找到占位符');
                    return;
                }

                // 检查所有占位符是否都有赋值
                const missingPlaceholders = [];
                placeholders.forEach(placeholder => {
                    if (!placeholderFactors[placeholder] || placeholderFactors[placeholder].length === 0) {
                        missingPlaceholders.push(placeholder);
                    }
                });

                if (missingPlaceholders.length > 0) {
                    alert(`以下占位符还没有赋值: ${missingPlaceholders.map(p => `<${p}/>`).join(', ')}\n请先搜索因子并赋值给这些占位符`);
                    return;
                }

                // 计算总组合数
                const totalCombinations = placeholders.reduce((total, placeholder) => {
                    return total * placeholderFactors[placeholder].length;
                }, 1);

                if (totalCombinations > 500) {
                    if (!confirm(`将生成 ${totalCombinations} 个公式，可能需要较长时间。是否继续？`)) {
                        return;
                    }
                }

                const formulas = [];

                // 生成所有组合
                generateAllCombinationsNew(placeholders, 0, {}, formulas, template);

                // 显示结果
                const formulaText = formulas.join(',\n');
                generatedFormulas.value = formulaText;
                formulaOutput.style.display = 'block';
                copyFormulasBtn.style.display = 'inline-block';
                exportFormulasBtn.style.display = 'inline-block';

                // 更新预览
                previewContent.innerHTML = `<strong>生成了 ${formulas.length} 个公式</strong><br>` +
                    formulas.slice(0, 5).map((f, i) => `${i + 1}. ${f}`).join('<br>') +
                    (formulas.length > 5 ? '<br>...' : '');
                formulaPreview.style.display = 'block';
            }

            function generateAllCombinationsNew(placeholders, currentIndex, currentAssignment, results, template) {
                if (currentIndex === placeholders.length) {
                    // 生成一个公式
                    let formula = template;
                    placeholders.forEach(placeholder => {
                        const factor = currentAssignment[placeholder];
                        formula = formula.replace(new RegExp(`<${placeholder}/>`, 'g'), factor.id);
                    });
                    results.push(formula);
                    return;
                }

                // 递归生成组合
                const placeholder = placeholders[currentIndex];
                const factors = placeholderFactors[placeholder];

                for (let factor of factors) {
                    currentAssignment[placeholder] = factor;
                    generateAllCombinationsNew(placeholders, currentIndex + 1, currentAssignment, results, template);
                    delete currentAssignment[placeholder];
                }
            }





            function copyFormulas() {
                generatedFormulas.select();
                document.execCommand('copy');
                alert('✅ 公式已复制到剪贴板');
            }

            function exportFormulas() {
                const formulas = generatedFormulas.value;
                if (!formulas) {
                    alert('没有可导出的公式');
                    return;
                }

                const blob = new Blob([formulas], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'generated_formulas.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                alert('✅ 公式已导出到文件');
            }
        });
    </script>
</body>
</html>
