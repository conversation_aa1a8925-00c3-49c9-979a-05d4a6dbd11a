# INFO 2025-08-22 17:15:54,674
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=ASI&delay=1&universe=MINVOL1M&instrumentType=EQUITY&search=market&theme=false&limit=1&offset=0
]: 

# INFO 2025-08-22 17:15:54,674
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 6316, 1000)]: 

# INFO 2025-08-22 17:16:24,384
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 6316, 1000)]: 

# INFO 2025-08-22 17:16:54,236
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=ASI&delay=1&universe=MINVOL1M&instrumentType=EQUITY&search=quarterly earnings&theme=false&limit=1&offset=0
]: 

# INFO 2025-08-22 17:16:54,241
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 6750, 1000)]: 

# INFO 2025-08-22 17:17:23,362
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 6750, 1000)]: 

# INFO 2025-08-22 17:22:57,465
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=ASI&delay=1&universe=MINVOL1M&instrumentType=EQUITY&search=quarterly earnings&category=fundamental&theme=false&limit=1&offset=0
]: 

# INFO 2025-08-22 17:22:57,469
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 1930, 1000)]: 

# INFO 2025-08-22 17:23:02,407
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 1930, 1000)]: 

# INFO 2025-08-22 17:23:58,147
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=ASI&delay=1&universe=MINVOL1M&instrumentType=EQUITY&search=quarterly earnings&category=fundamental&theme=false&limit=1&offset=0
]: 

# INFO 2025-08-22 17:23:58,150
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 1930, 1000)]: 

# INFO 2025-08-22 17:24:01,081
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 1930, 1000)]: 

