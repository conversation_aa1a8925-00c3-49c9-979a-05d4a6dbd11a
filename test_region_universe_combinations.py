#!/usr/bin/env python3
"""
测试Region-Universe组合的有效性
"""

import requests
import json
import time

def test_get_available_universes():
    """测试获取可用universe的API"""
    print("🌐 测试获取可用universe的API...")
    
    regions = ['USA', 'EUR', 'CHN', 'ASI', 'GLB']
    delays = [0, 1]
    
    for region in regions:
        for delay in delays:
            print(f"\n--- {region} (delay={delay}) ---")
            
            try:
                response = requests.get(
                    f'http://localhost:5000/get_available_universes?region={region}&delay={delay}',
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        universes = result.get('available_universes', [])
                        print(f"✅ 可用universe ({len(universes)}个): {universes}")
                    else:
                        print(f"❌ API返回失败")
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求错误: {e}")

def test_valid_combinations():
    """测试有效的region-universe组合"""
    print("\n🔍 测试有效的region-universe组合...")
    
    # 基于data文件夹的已知有效组合
    valid_combinations = [
        ('USA', 1, 'TOP3000'),
        ('EUR', 1, 'TOP1200'),
        ('CHN', 1, 'TOP2000U'),
        ('ASI', 1, 'MINVOL1M'),
        ('GLB', 1, 'TOP3000'),
    ]
    
    for region, delay, universe in valid_combinations:
        print(f"\n--- 测试 {region}-{delay}-{universe} ---")
        
        test_data = {
            'region': region,
            'delay': delay,
            'universe': universe,
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    print(f"✅ 搜索成功: {len(results)} 个结果")
                    
                    if results:
                        first_result = results[0]
                        actual_region = first_result.get('region', 'N/A')
                        print(f"📊 返回数据region: {actual_region}")
                        
                        if actual_region == region:
                            print(f"✅ Region数据一致！")
                        else:
                            print(f"⚠️ Region数据不一致: 期望{region}, 实际{actual_region}")
                else:
                    print(f"❌ 搜索失败: {result.get('error')}")
            elif response.status_code == 401:
                print("❌ 需要先登录")
                return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)
    
    return True

def test_invalid_combinations():
    """测试无效的region-universe组合"""
    print("\n🚫 测试无效的region-universe组合...")
    
    # 已知无效的组合
    invalid_combinations = [
        ('ASI', 1, 'TOP3000'),  # ASI没有TOP3000
        ('EUR', 1, 'TOP3000'),  # EUR没有TOP3000
        ('CHN', 1, 'TOP3000'),  # CHN没有TOP3000
        ('USA', 1, 'TOP2000U'), # USA没有TOP2000U
    ]
    
    for region, delay, universe in invalid_combinations:
        print(f"\n--- 测试无效组合 {region}-{delay}-{universe} ---")
        
        test_data = {
            'region': region,
            'delay': delay,
            'universe': universe,
            'search': 'price',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 5,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=30)
            
            if response.status_code == 400:
                result = response.json()
                error_msg = result.get('error', '')
                print(f"✅ 正确拒绝无效组合")
                print(f"💬 错误消息: {error_msg}")
            elif response.status_code == 200:
                print(f"❌ 应该拒绝无效组合，但请求成功了")
            else:
                print(f"⚠️ 意外的HTTP状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(1)

def test_asi_with_correct_universe():
    """专门测试ASI地区与正确的universe组合"""
    print("\n🎯 专门测试ASI地区...")
    
    # ASI地区的有效universe
    asi_combinations = [
        ('ASI', 1, 'MINVOL1M'),
        ('ASI', 1, 'ILLIQUID_MINVOL1M'),
    ]
    
    for region, delay, universe in asi_combinations:
        print(f"\n--- 测试ASI组合 {region}-{delay}-{universe} ---")
        
        test_data = {
            'region': region,
            'delay': delay,
            'universe': universe,
            'search': 'close',
            'category': 'pv',
            'type': 'MATRIX',
            'limit': 10,
            'use_api': True
        }
        
        try:
            response = requests.post('http://localhost:5000/search', json=test_data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    print(f"✅ ASI搜索成功: {len(results)} 个结果")
                    
                    if results:
                        # 检查前几个结果的region
                        regions_in_results = set(r.get('region', 'N/A') for r in results[:5])
                        print(f"📊 结果中的region: {regions_in_results}")
                        
                        if 'ASI' in regions_in_results:
                            print(f"✅ 确实返回了ASI地区的数据！")
                        else:
                            print(f"⚠️ 没有找到ASI地区的数据")
                            
                        # 显示一些结果
                        for i, result in enumerate(results[:3], 1):
                            print(f"   {i}. {result.get('id', 'N/A')} (region: {result.get('region', 'N/A')})")
                else:
                    print(f"❌ ASI搜索失败: {result.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(2)

def main():
    print("=" * 60)
    print("🔧 Region-Universe组合测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常\n")
    
    # 执行测试
    test_get_available_universes()
    
    valid_ok = test_valid_combinations()
    
    if valid_ok:
        test_invalid_combinations()
        test_asi_with_correct_universe()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if valid_ok:
        print("🎉 Region-Universe组合测试完成！")
        print("\n✅ 修复内容:")
        print("   - 恢复了所有地区: USA, EUR, CHN, ASI, GLB")
        print("   - 添加了region-universe组合验证")
        print("   - 提供了可用universe的查询API")
        print("   - 给出了明确的错误提示和建议")
        print("\n💡 现在用户可以:")
        print("   - 选择任何地区")
        print("   - 系统会验证region-universe组合的有效性")
        print("   - 获得准确的错误提示和可用选项建议")
    else:
        print("⚠️ 需要先登录才能完成测试")

if __name__ == "__main__":
    main()
