#!/usr/bin/env python3
"""
数据字段检索器 Web 服务器
基于Flask的Web服务，提供数据字段搜索功能
"""

from flask import Flask, request, jsonify, send_from_directory
import pandas as pd
import os
import json
from typing import List, Dict, Any
import re

app = Flask(__name__)

class DataSearcher:
    def __init__(self, data_dir: str = "data/split_files"):
        self.data_dir = data_dir
        self.data_cache = {}
        self._load_data()
    
    def _load_data(self):
        """加载所有CSV数据文件到内存"""
        print("正在加载数据文件...")
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        
        for file in csv_files:
            file_path = os.path.join(self.data_dir, file)
            try:
                df = pd.read_csv(file_path)
                # 从文件名解析region, delay, universe
                parts = file.replace('.csv', '').split('_')
                if len(parts) >= 3:
                    region = parts[0]
                    delay = int(parts[1])
                    universe = '_'.join(parts[2:])
                    
                    key = f"{region}_{delay}_{universe}"
                    self.data_cache[key] = df
                    print(f"已加载: {key} ({len(df)} 条记录)")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        print(f"数据加载完成，共加载 {len(self.data_cache)} 个数据集")
    
    def search_fields(self, region: str, delay: int, universe: str, 
                     search: str = "", category: str = "", 
                     data_type: str = "", limit: int = 1000) -> List[Dict[str, Any]]:
        """搜索数据字段"""
        
        # 构建数据集键
        key = f"{region}_{delay}_{universe}"
        
        if key not in self.data_cache:
            raise ValueError(f"未找到数据集: {key}")
        
        df = self.data_cache[key].copy()
        
        # 应用过滤条件
        if category:
            df = df[df['category.id'] == category]
        
        if data_type:
            df = df[df['type'] == data_type]
        
        if search:
            # 在id和description字段中搜索
            search_pattern = re.compile(search, re.IGNORECASE)
            mask = (df['id'].astype(str).str.contains(search, case=False, na=False) | 
                   df['description'].astype(str).str.contains(search, case=False, na=False))
            df = df[mask]
        
        # 限制结果数量
        df = df.head(limit)
        
        # 转换为字典列表
        results = df.to_dict('records')
        
        return results

# 创建搜索器实例
searcher = DataSearcher()

@app.route('/')
def index():
    """提供主页"""
    return send_from_directory('.', 'web_interface.html')

@app.route('/search', methods=['POST'])
def search():
    """处理搜索请求"""
    try:
        data = request.get_json()
        
        # 提取参数
        region = data.get('region', 'USA')
        delay = int(data.get('delay', 1))
        universe = data.get('universe', 'TOP3000')
        search_term = data.get('search', '')
        category = data.get('category', '')
        data_type = data.get('type', '')
        limit = int(data.get('limit', 1000))
        
        # 执行搜索
        results = searcher.search_fields(
            region=region,
            delay=delay,
            universe=universe,
            search=search_term,
            category=category,
            data_type=data_type,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'results': results,
            'count': len(results),
            'params': {
                'region': region,
                'delay': delay,
                'universe': universe,
                'search': search_term,
                'category': category,
                'type': data_type,
                'limit': limit
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400

@app.route('/api/info')
def api_info():
    """提供API信息"""
    return jsonify({
        'available_datasets': list(searcher.data_cache.keys()),
        'total_records': sum(len(df) for df in searcher.data_cache.values()),
        'regions': ['USA', 'EUR', 'ASI', 'CHN', 'GLB'],
        'delays': [0, 1],
        'universes': ['TOP3000', 'TOP2500', 'TOP2000U', 'TOP1200', 'TOP1000', 
                     'TOP800', 'TOPSP500', 'TOP500', 'TOP400', 'TOP200', 
                     'TOPDIV3000', 'MINVOL1M', 'ILLIQUID_MINVOL1M'],
        'types': ['MATRIX', 'VECTOR', 'GROUP', 'UNIVERSE', 'SYMBOL'],
        'categories': [
            {'id': 'analyst', 'name': 'Analyst'},
            {'id': 'fundamental', 'name': 'Fundamental'},
            {'id': 'model', 'name': 'Model'},
            {'id': 'news', 'name': 'News'},
            {'id': 'option', 'name': 'Option'},
            {'id': 'other', 'name': 'Other'},
            {'id': 'pv', 'name': 'Price Volume'},
            {'id': 'risk', 'name': 'Risk'},
            {'id': 'sentiment', 'name': 'Sentiment'},
            {'id': 'shortinterest', 'name': 'Short Interest'},
            {'id': 'socialmedia', 'name': 'Social Media'}
        ]
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("启动数据字段检索器 Web 服务...")
    print("访问 http://localhost:5000 来使用界面")
    app.run(debug=True, host='0.0.0.0', port=5000)
