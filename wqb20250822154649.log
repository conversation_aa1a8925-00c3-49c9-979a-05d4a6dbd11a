# INFO 2025-08-22 15:46:52,735
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=USA&delay=1&universe=TOP3000&instrumentType=EQUITY&search=market&category=pv&theme=false&type=MATRIX&limit=1&offset=0
]: 

# INFO 2025-08-22 15:46:52,736
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 1755, 1000)]: 

# INFO 2025-08-22 15:46:54,878
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 1755, 1000)]: 

# INFO 2025-08-22 15:46:57,968
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=USA&delay=1&universe=TOP3000&instrumentType=EQUITY&search=earnings&category=analyst&theme=false&type=MATRIX&limit=1&offset=0
]: 

# INFO 2025-08-22 15:46:57,968
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 6569, 10)]: 

# INFO 2025-08-22 15:47:04,124
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=USA&delay=1&universe=TOP3000&instrumentType=EQUITY&search=market&category=pv&theme=false&type=MATRIX&limit=1&offset=0
]: 

# INFO 2025-08-22 15:47:04,125
<WQBSession ['<EMAIL>']>.search_fields(...) [start range(0, 1755, 1000)]: 

# INFO 2025-08-22 15:47:07,224
<WQBSession ['<EMAIL>']>.search_fields(...) [finish range(0, 1755, 1000)]: 

# INFO 2025-08-22 15:48:56,027
<WQBSession ['<EMAIL>']>.search_fields_limited(...) [
    https://api.worldquantbrain.com/data-fields?region=USA&delay=1&universe=TOP3000&instrumentType=EQUITY&search=earnings&category=analyst&theme=false&type=MATRIX&limit=10&offset=990
]: 100/657 = 15%

