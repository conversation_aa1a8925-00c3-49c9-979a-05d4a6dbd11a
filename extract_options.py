import pandas as pd
import os
from pathlib import Path

def extract_unique_values():
    """从CSV文件中提取region, delay, universe, type的唯一值"""
    data_folder = Path('data/split_files')
    
    all_regions = set()
    all_delays = set()
    all_universes = set()
    all_types = set()
    
    # 遍历所有CSV文件
    for csv_file in data_folder.glob('*.csv'):
        try:
            df = pd.read_csv(csv_file)
            
            if 'region' in df.columns:
                all_regions.update(df['region'].unique())
            if 'delay' in df.columns:
                all_delays.update(df['delay'].unique())
            if 'universe' in df.columns:
                all_universes.update(df['universe'].unique())
            if 'type' in df.columns:
                all_types.update(df['type'].unique())
                
        except Exception as e:
            print(f"Error reading {csv_file}: {e}")
    
    print("Region 选项:")
    for region in sorted(all_regions):
        print(f"  - {region}")
    
    print("\nDelay 选项:")
    for delay in sorted(all_delays):
        print(f"  - {delay}")
    
    print("\nUniverse 选项:")
    for universe in sorted(all_universes):
        print(f"  - {universe}")
    
    print("\nType 选项:")
    for data_type in sorted(all_types):
        print(f"  - {data_type}")

if __name__ == "__main__":
    extract_unique_values()
