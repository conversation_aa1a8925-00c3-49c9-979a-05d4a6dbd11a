#!/usr/bin/env python3
"""
测试登录功能
"""

import requests
import json
import time

def test_login_status():
    """测试登录状态检查"""
    print("1️⃣ 测试登录状态检查")
    print("-" * 40)
    
    try:
        response = requests.get('http://localhost:5000/login_status', timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录状态检查成功")
            print(f"📊 登录状态: {'已登录' if result.get('logged_in') else '未登录'}")
            if result.get('logged_in'):
                print(f"👤 用户ID: {result.get('user_id', 'N/A')}")
            return result.get('logged_in', False)
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

def test_login(username, password):
    """测试登录功能"""
    print(f"\n2️⃣ 测试登录功能")
    print("-" * 40)
    
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/login', 
            json=login_data, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登录成功！")
                print(f"👤 用户ID: {result.get('user_id', 'N/A')}")
                print(f"💬 消息: {result.get('message', 'N/A')}")
                return True
            else:
                print(f"❌ 登录失败: {result.get('error', '未知错误')}")
                return False
        else:
            result = response.json()
            print(f"❌ 登录失败 (HTTP {response.status_code}): {result.get('error', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 登录请求错误: {e}")
        return False

def test_api_search_after_login():
    """测试登录后的API搜索"""
    print(f"\n3️⃣ 测试登录后的API搜索")
    print("-" * 40)
    
    search_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 3,
        'use_api': True
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://localhost:5000/search', 
            json=search_data, 
            timeout=60
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API搜索成功！")
                print(f"⏱️  响应时间: {end_time - start_time:.2f} 秒")
                print(f"📊 找到结果: {result.get('count', 0)} 个")
                print(f"📋 返回结果: {len(result.get('results', []))} 个")
                
                if result.get('results'):
                    first = result['results'][0]
                    print(f"🔍 示例: {first.get('id', 'N/A')} - {first.get('description', 'N/A')[:50]}...")
                return True
            else:
                print(f"❌ API搜索失败: {result.get('error', '未知错误')}")
                return False
        else:
            result = response.json()
            print(f"❌ API搜索失败 (HTTP {response.status_code}): {result.get('error', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ API搜索请求错误: {e}")
        return False

def test_logout():
    """测试登出功能"""
    print(f"\n4️⃣ 测试登出功能")
    print("-" * 40)
    
    try:
        response = requests.post('http://localhost:5000/logout', timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 登出成功！")
                print(f"💬 消息: {result.get('message', 'N/A')}")
                return True
            else:
                print(f"❌ 登出失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 登出HTTP错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登出请求错误: {e}")
        return False

def test_api_search_without_login():
    """测试未登录时的API搜索"""
    print(f"\n5️⃣ 测试未登录时的API搜索")
    print("-" * 40)
    
    search_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'price',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 3,
        'use_api': True
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/search', 
            json=search_data, 
            timeout=10
        )
        
        if response.status_code == 401:
            result = response.json()
            print(f"✅ 正确拒绝未登录的API搜索")
            print(f"💬 错误消息: {result.get('error', 'N/A')}")
            return True
        else:
            print(f"❌ 应该拒绝未登录的API搜索，但返回了: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

def main():
    print("=" * 60)
    print("🔐 数据字段检索器 - 登录功能测试")
    print("=" * 60)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行")
            return
    except:
        print("❌ 无法连接到服务")
        return
    
    print("✅ 服务运行正常，开始测试...\n")
    
    # 测试序列
    tests = []
    
    # 1. 检查初始登录状态
    initial_logged_in = test_login_status()
    tests.append(("初始登录状态检查", not initial_logged_in))  # 应该是未登录状态
    
    # 2. 测试未登录时的API搜索
    no_login_rejected = test_api_search_without_login()
    tests.append(("未登录API搜索拒绝", no_login_rejected))
    
    # 3. 测试登录（需要用户输入）
    print(f"\n请输入测试用的API账户信息:")
    username = input("用户名/邮箱: ").strip()
    password = input("密码: ").strip()
    
    if username and password:
        login_success = test_login(username, password)
        tests.append(("用户登录", login_success))
        
        if login_success:
            # 4. 测试登录后的API搜索
            api_search_success = test_api_search_after_login()
            tests.append(("登录后API搜索", api_search_success))
            
            # 5. 测试登出
            logout_success = test_logout()
            tests.append(("用户登出", logout_success))
            
            # 6. 再次检查登录状态
            final_logged_in = test_login_status()
            tests.append(("登出后状态检查", not final_logged_in))  # 应该是未登录状态
    else:
        print("⚠️ 跳过需要账户信息的测试")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name:<20}: {status}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！登录功能完全正常！")
        print("\n💡 使用说明:")
        print("   1. 在网页中点击'🔑 API登录'按钮")
        print("   2. 输入您的WQB账户信息")
        print("   3. 登录成功后即可使用API搜索功能")
        print("   4. 点击'👤 已登录'可以登出")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
