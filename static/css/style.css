body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar {
    background-color: #343a40;
    color: white;
    min-height: 100vh;
    padding: 20px;
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
}

.sidebar-header {
    margin-bottom: 30px;
    text-align: center;
    border-bottom: 1px solid #495057;
    padding-bottom: 20px;
}

.sidebar-header h4 {
    color: #17a2b8;
    margin: 0;
}

.sidebar .form-label {
    color: #adb5bd;
    font-weight: 500;
    margin-bottom: 5px;
}

.sidebar .form-control,
.sidebar .form-select {
    background-color: #495057;
    border: 1px solid #6c757d;
    color: white;
}

.sidebar .form-control:focus,
.sidebar .form-select:focus {
    background-color: #495057;
    border-color: #17a2b8;
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.sidebar .form-control::placeholder {
    color: #adb5bd;
}

.sidebar .form-check-label {
    color: #adb5bd;
}

.main-content {
    margin-left: 25%;
    padding: 20px;
}

.content-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-header h2 {
    margin: 0;
    color: #343a40;
}

.results-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 400px;
}

.welcome-message {
    padding: 40px;
    text-align: center;
    color: #6c757d;
}

.welcome-message i {
    font-size: 3rem;
    color: #17a2b8;
    margin-bottom: 20px;
}

.welcome-message h4 {
    color: #343a40;
    margin-bottom: 15px;
}

.welcome-message ul {
    text-align: left;
    max-width: 600px;
    margin: 20px auto;
}

.result-item {
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    transition: background-color 0.2s;
}

.result-item:hover {
    background-color: #f8f9fa;
}

.result-item:last-child {
    border-bottom: none;
}

.result-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.result-id {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
    font-size: 1.1rem;
}

.result-type {
    background-color: #6c757d;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 10px;
}

.result-type.MATRIX { background-color: #28a745; }
.result-type.VECTOR { background-color: #17a2b8; }
.result-type.GROUP { background-color: #ffc107; color: #212529; }
.result-type.UNIVERSE { background-color: #dc3545; }
.result-type.SYMBOL { background-color: #6f42c1; }

.result-description {
    color: #495057;
    margin-bottom: 10px;
    line-height: 1.5;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.9rem;
    color: #6c757d;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.meta-item i {
    width: 16px;
    text-align: center;
}

.category-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.coverage-bar {
    width: 100px;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-left: 5px;
}

.coverage-fill {
    height: 100%;
    background-color: #28a745;
    transition: width 0.3s ease;
}

#loading {
    padding: 40px;
}

.alert {
    margin: 20px;
}

.btn-primary {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-primary:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.btn-info {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-info:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        min-height: auto;
        width: 100%;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
