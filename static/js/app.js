document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const testConnectionBtn = document.getElementById('testConnection');
    const loadingDiv = document.getElementById('loading');
    const errorDiv = document.getElementById('error');
    const resultsDiv = document.getElementById('results');
    const resultCountDiv = document.getElementById('resultCount');

    // 搜索表单提交
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch();
    });

    // 测试连接按钮
    testConnectionBtn.addEventListener('click', function() {
        testConnection();
    });

    function performSearch() {
        const formData = new FormData(searchForm);
        const searchData = {
            region: formData.get('region'),
            delay: formData.get('delay'),
            universe: formData.get('universe'),
            search: formData.get('search'),
            category: formData.get('category'),
            type: formData.get('type'),
            limit: formData.get('limit'),
            use_api: formData.get('use_api') === 'on'
        };

        showLoading();
        hideError();

        fetch('/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(searchData)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayResults(data.results, data.count);
            } else {
                showError(data.error || '搜索失败');
            }
        })
        .catch(error => {
            hideLoading();
            showError('网络错误: ' + error.message);
        });
    }

    function testConnection() {
        const originalText = testConnectionBtn.innerHTML;
        testConnectionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
        testConnectionBtn.disabled = true;

        fetch('/test_connection')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`API连接成功！用户ID: ${data.user_id}`);
            } else {
                showError(`API连接失败: ${data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            showError('连接测试失败: ' + error.message);
        })
        .finally(() => {
            testConnectionBtn.innerHTML = originalText;
            testConnectionBtn.disabled = false;
        });
    }

    function displayResults(results, count) {
        resultCountDiv.textContent = `找到 ${count} 个结果`;
        
        if (results.length === 0) {
            resultsDiv.innerHTML = `
                <div class="welcome-message">
                    <i class="fas fa-search"></i>
                    <h4>未找到匹配的结果</h4>
                    <p>请尝试调整搜索条件或使用不同的关键词。</p>
                </div>
            `;
            return;
        }

        let html = '';
        results.forEach(result => {
            html += createResultItem(result);
        });
        
        resultsDiv.innerHTML = html;
    }

    function createResultItem(result) {
        const coverage = result.coverage ? (result.coverage * 100).toFixed(1) : 'N/A';
        const userCount = result.userCount || result.user_count || 'N/A';
        const alphaCount = result.alphaCount || result.alpha_count || 'N/A';

        // 处理不同的数据结构（CSV vs API）
        let categoryName = 'N/A';
        if (result['category.name']) {
            categoryName = result['category.name']; // CSV格式
        } else if (result.category && result.category.name) {
            categoryName = result.category.name; // API格式
        } else if (result.category_name) {
            categoryName = result.category_name; // 其他格式
        }

        let datasetName = 'N/A';
        if (result['dataset.name']) {
            datasetName = result['dataset.name']; // CSV格式
        } else if (result.dataset && result.dataset.name) {
            datasetName = result.dataset.name; // API格式
        } else if (result.dataset_name) {
            datasetName = result.dataset_name; // 其他格式
        }

        return `
            <div class="result-item">
                <div class="result-header">
                    <div>
                        <span class="result-id">${result.id}</span>
                        <span class="result-type ${result.type}">${result.type}</span>
                    </div>
                </div>
                <div class="result-description">
                    ${result.description || '无描述'}
                </div>
                <div class="result-meta">
                    <div class="meta-item">
                        <i class="fas fa-tag"></i>
                        <span class="category-badge">${categoryName}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>覆盖率: ${coverage}%</span>
                        ${coverage !== 'N/A' ? `
                            <div class="coverage-bar">
                                <div class="coverage-fill" style="width: ${coverage}%"></div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-users"></i>
                        <span>用户数: ${userCount}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calculator"></i>
                        <span>Alpha数: ${alphaCount}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-database"></i>
                        <span>数据集: ${datasetName}</span>
                    </div>
                </div>
            </div>
        `;
    }

    function showLoading() {
        loadingDiv.style.display = 'block';
        resultsDiv.style.display = 'none';
        resultCountDiv.textContent = '';
    }

    function hideLoading() {
        loadingDiv.style.display = 'none';
        resultsDiv.style.display = 'block';
    }

    function showError(message) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    function hideError() {
        errorDiv.style.display = 'none';
    }

    function showSuccess(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success';
        successDiv.textContent = message;
        successDiv.style.position = 'fixed';
        successDiv.style.top = '20px';
        successDiv.style.right = '20px';
        successDiv.style.zIndex = '9999';
        
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
            successDiv.remove();
        }, 3000);
    }
});
