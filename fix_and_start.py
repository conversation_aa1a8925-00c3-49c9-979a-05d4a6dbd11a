#!/usr/bin/env python3
"""
修复依赖兼容性问题并启动应用
专门解决 googletrans 与 httpcore 的兼容性问题
"""

import os
import sys
import subprocess

def print_banner():
    print("=" * 60)
    print("🔧 依赖修复 & 启动脚本")
    print("=" * 60)

def fix_translation_dependencies():
    """修复翻译相关依赖的兼容性问题"""
    print("🔧 修复翻译库兼容性问题...")
    
    try:
        # 卸载可能冲突的包
        print("   卸载冲突的包...")
        packages_to_remove = ["googletrans", "httpcore", "httpx"]
        for package in packages_to_remove:
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "-y", package], 
                          capture_output=True)
        
        # 安装兼容的版本
        print("   安装兼容版本...")
        compatible_packages = [
            "httpcore==0.9.1",
            "httpx==0.13.3",
            "googletrans==3.1.0a0"
        ]
        
        for package in compatible_packages:
            print(f"   安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                   capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ {package} 安装成功")
            else:
                print(f"   ⚠️ {package} 安装失败")
                
        print("✅ 兼容性修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

def install_other_dependencies():
    """安装其他必需依赖"""
    print("📦 安装其他依赖...")
    
    other_packages = [
        "flask>=2.0.0",
        "pandas>=1.3.0", 
        "wqb",
        "requests>=2.25.0",
        "urllib3>=1.26.0"
    ]
    
    for package in other_packages:
        try:
            print(f"   检查 {package.split('>=')[0].split('==')[0]}...")
            __import__(package.split('>=')[0].split('==')[0])
            print(f"   ✅ {package.split('>=')[0].split('==')[0]} 已安装")
        except ImportError:
            print(f"   安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                   capture_output=True)
            if result.returncode == 0:
                print(f"   ✅ {package} 安装成功")
            else:
                print(f"   ❌ {package} 安装失败")

def start_app():
    """启动应用"""
    print("\n🚀 启动数据字段检索器...")
    print("📍 访问地址: http://127.0.0.1:5000")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        import app
        app.app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 尝试备用启动方法...")
        subprocess.run([sys.executable, "app.py"])

def main():
    print_banner()
    
    print("🔍 这个脚本将修复 googletrans 兼容性问题并启动应用")
    print("⚠️  如果您遇到 'httpcore' 相关错误，这个脚本可以解决")
    print()
    
    # 1. 修复翻译依赖
    if not fix_translation_dependencies():
        print("⚠️ 翻译功能修复失败，但应用仍可使用（无翻译功能）")
    
    # 2. 安装其他依赖
    install_other_dependencies()
    
    # 3. 启动应用
    print("\n" + "=" * 60)
    start_app()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 脚本执行出错: {e}")
        print("\n💡 如果问题持续，请尝试手动安装:")
        print("   pip uninstall googletrans httpcore httpx")
        print("   pip install httpcore==0.9.1 httpx==0.13.3 googletrans==3.1.0a0")
        print("   python app.py")
        sys.exit(1)
