from flask import Flask, render_template, request, jsonify
import wqb
import pandas as pd
from pathlib import Path
import json

app = Flask(__name__)

# 初始化WQB会话
logger = wqb.wqb_logger()
wqbs = wqb.WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)

# 设置更长的超时时间和重试机制
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def setup_session_with_retries():
    """设置带重试机制的session"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 设置更长的超时时间
    session.timeout = 60

    return session

# 为wqbs设置自定义session
try:
    custom_session = setup_session_with_retries()
    if hasattr(wqbs, '_session'):
        wqbs._session = custom_session
    elif hasattr(wqbs, 'session'):
        wqbs.session = custom_session
except Exception as e:
    print(f"设置自定义session失败: {e}")

# 添加SSL错误处理
import ssl
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置选项
REGIONS = ['USA', 'EUR', 'CHN', 'ASI', 'GLB']
DELAYS = [0, 1]
UNIVERSES = ['TOP3000', 'TOP1000', 'TOP500', 'TOP200', 'TOPSP500', 'TOP1200', 'TOP2500', 'TOP400', 'TOP800', 'TOP2000U', 'TOPDIV3000', 'MINVOL1M', 'ILLIQUID_MINVOL1M']
TYPES = ['MATRIX', 'VECTOR', 'GROUP', 'UNIVERSE', 'SYMBOL']
CATEGORIES = [
    {'id': 'analyst', 'name': 'Analyst'},
    {'id': 'fundamental', 'name': 'Fundamental'},
    {'id': 'model', 'name': 'Model'},
    {'id': 'news', 'name': 'News'},
    {'id': 'option', 'name': 'Option'},
    {'id': 'other', 'name': 'Other'},
    {'id': 'pv', 'name': 'Price Volume'},
    {'id': 'risk', 'name': 'Risk'},
    {'id': 'sentiment', 'name': 'Sentiment'},
    {'id': 'shortinterest', 'name': 'Short Interest'},
    {'id': 'socialmedia', 'name': 'Social Media'}
]

def load_local_data(region, delay, universe):
    """从本地CSV文件加载数据"""
    filename = f"{region}_{delay}_{universe}.csv"
    file_path = Path(f"data/split_files/{filename}")
    
    if file_path.exists():
        try:
            df = pd.read_csv(file_path)
            return df
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    return None

@app.route('/')
def index():
    return render_template('index.html',
                         regions=REGIONS,
                         delays=DELAYS,
                         universes=UNIVERSES,
                         types=TYPES,
                         categories=CATEGORIES)

@app.route('/test')
def test_page():
    from flask import send_file
    return send_file('test_frontend.html')

@app.route('/search', methods=['POST'])
def search():
    try:
        data = request.json
        region = data.get('region', 'USA')
        delay = int(data.get('delay', 1))
        universe = data.get('universe', 'TOP3000')
        search_term = data.get('search', '')
        category = data.get('category', '')
        data_type = data.get('type', '')
        limit = int(data.get('limit', 100))
        use_api = data.get('use_api', False)
        
        results = []
        
        if use_api:
            # 使用API搜索
            try:
                print(f"开始API搜索: region={region}, delay={delay}, universe={universe}")
                print(f"搜索参数: search={search_term}, category={category}, type={data_type}, limit={limit}")

                # 重新创建session以避免连接问题
                try:
                    # 测试连接
                    test_resp = wqbs.auth_request()
                    if not test_resp.ok:
                        return jsonify({'error': f'API认证失败: {test_resp.status_code}'}), 400
                    print("API认证成功")
                except Exception as auth_e:
                    print(f"API认证异常: {auth_e}")
                    return jsonify({'error': f'API认证错误: {str(auth_e)}'}), 500

                # 完全按照12.py的方式调用
                resps = wqbs.search_fields(
                    region,
                    delay,
                    universe,
                    search=search_term if search_term else None,
                    category=category if category else None,
                    theme=False,  # 重要：添加这个参数
                    type=data_type if data_type else None,
                    limit=limit
                )

                # 处理所有API响应，不设置限制
                print("开始处理API响应...")

                for i, resp in enumerate(resps, 1):
                    print(f"处理响应 {i}: status={resp.status_code}")
                    if resp.ok:
                        api_data = resp.json()
                        api_results = api_data.get('results', [])
                        print(f"响应 {i} 数据量: {len(api_results)} 个结果")

                        # 添加所有结果
                        results.extend(api_results)

                        # 如果已经获得足够的结果，就停止
                        if len(results) >= limit:
                            print(f"已获得 {len(results)} 个结果，达到limit限制")
                            results = results[:limit]  # 截取到指定数量
                            break
                    else:
                        error_msg = f'API请求失败: HTTP {resp.status_code}'
                        try:
                            error_detail = resp.text
                            if error_detail:
                                error_msg += f' - {error_detail}'
                        except:
                            pass
                        print(f"API错误: {error_msg}")
                        return jsonify({'error': error_msg}), 400

                print(f"API搜索完成，总共获得 {len(results)} 个结果")

            except Exception as e:
                import traceback
                error_trace = traceback.format_exc()
                print(f"API调用异常: {e}")
                print(f"异常详情: {error_trace}")

                # 提供更友好的错误信息
                error_msg = str(e)
                if 'SSL' in error_msg or 'ssl' in error_msg.lower():
                    error_msg = "SSL连接错误，请检查网络连接或稍后重试"
                elif 'timeout' in error_msg.lower():
                    error_msg = "请求超时，请稍后重试或减少搜索结果数量"
                elif 'connection' in error_msg.lower():
                    error_msg = "网络连接错误，请检查网络状态"

                return jsonify({'error': f'API调用错误: {error_msg}'}), 500
        else:
            # 使用本地数据搜索
            df = load_local_data(region, delay, universe)
            if df is None:
                return jsonify({'error': f'未找到数据文件: {region}_{delay}_{universe}.csv'}), 404
            
            # 应用过滤条件
            filtered_df = df.copy()
            
            if search_term:
                # 在description字段中搜索
                filtered_df = filtered_df[
                    filtered_df['description'].str.contains(search_term, case=False, na=False)
                ]
            
            if category:
                filtered_df = filtered_df[filtered_df['category.id'] == category]
            
            if data_type:
                filtered_df = filtered_df[filtered_df['type'] == data_type]
            
            # 限制结果数量
            filtered_df = filtered_df.head(limit)
            
            # 转换为字典列表
            results = filtered_df.to_dict('records')
        
        return jsonify({
            'success': True,
            'count': len(results),
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'搜索错误: {str(e)}'}), 500

@app.route('/test_connection')
def test_connection():
    """测试API连接"""
    try:
        resp = wqbs.auth_request()
        return jsonify({
            'success': resp.ok,
            'status_code': resp.status_code,
            'user_id': resp.json().get('user', {}).get('id') if resp.ok else None
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/favicon.ico')
def favicon():
    """返回favicon"""
    return '', 204

@app.route('/apple-touch-icon.png')
@app.route('/apple-touch-icon-precomposed.png')
def apple_touch_icon():
    """返回苹果触摸图标"""
    return '', 204

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
