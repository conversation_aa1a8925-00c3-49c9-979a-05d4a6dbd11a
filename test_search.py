#!/usr/bin/env python3
"""
测试搜索功能的脚本
"""

import requests
import json
import time

def test_api_info():
    """测试API信息接口"""
    print("🔍 测试API信息接口...")
    try:
        response = requests.get('http://localhost:5000/api/info')
        if response.ok:
            data = response.json()
            print("✅ API信息获取成功")
            print(f"   服务: {data.get('service')}")
            print(f"   版本: {data.get('version')}")
            print(f"   WQB连接: {data.get('wqb_connected')}")
            return True
        else:
            print(f"❌ API信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API信息获取异常: {e}")
        return False

def test_connection():
    """测试WQB连接"""
    print("\n🌐 测试WQB连接...")
    try:
        response = requests.get('http://localhost:5000/test-connection')
        if response.ok:
            data = response.json()
            if data.get('connected'):
                print("✅ WQB连接正常")
                return True
            else:
                print(f"❌ WQB连接失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 连接测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False

def test_search(search_params, test_name):
    """测试搜索功能"""
    print(f"\n🔍 测试搜索: {test_name}")
    print(f"   参数: {search_params}")
    
    try:
        response = requests.post(
            'http://localhost:5000/search',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(search_params)
        )
        
        if response.ok:
            data = response.json()
            if data.get('success'):
                count = data.get('count', 0)
                print(f"✅ 搜索成功，找到 {count} 条结果")
                
                # 显示前3个结果
                results = data.get('results', [])
                for i, result in enumerate(results[:3], 1):
                    print(f"   结果 {i}:")
                    print(f"     ID: {result.get('id', 'N/A')}")
                    print(f"     描述: {result.get('description', 'N/A')[:50]}...")
                    print(f"     类型: {result.get('type', 'N/A')}")
                
                return True
            else:
                print(f"❌ 搜索失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 搜索请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 数据字段检索器功能测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试API信息
    if not test_api_info():
        print("\n❌ 基础API测试失败，请检查服务器是否正常运行")
        return
    
    # 测试WQB连接
    if not test_connection():
        print("\n⚠️  WQB连接测试失败，但可以继续测试其他功能")
    
    # 测试搜索功能
    test_cases = [
        {
            'params': {
                'region': 'USA',
                'delay': 1,
                'universe': 'TOP3000',
                'search': 'earnings',
                'category': 'analyst',
                'type': 'MATRIX',
                'limit': 10
            },
            'name': '分析师收益数据'
        },
        {
            'params': {
                'region': 'USA',
                'delay': 0,
                'universe': 'TOPSP500',
                'search': 'price',
                'category': 'pv',
                'type': '',
                'limit': 5
            },
            'name': '价格数据'
        },
        {
            'params': {
                'region': 'GLB',
                'delay': 1,
                'universe': 'TOP3000',
                'search': 'revenue',
                'category': 'fundamental',
                'type': 'VECTOR',
                'limit': 8
            },
            'name': '基本面收入数据'
        }
    ]
    
    success_count = 0
    for test_case in test_cases:
        if test_search(test_case['params'], test_case['name']):
            success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"✅ 成功测试: {success_count}/{len(test_cases)}")
    
    if success_count == len(test_cases):
        print("🎉 所有测试通过！系统运行正常")
        print("\n💡 使用提示:")
        print("   1. 访问 http://localhost:5000 使用Web界面")
        print("   2. 尝试不同的搜索关键词和过滤条件")
        print("   3. 查看搜索结果的详细信息")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
    
    print("\n🔗 相关链接:")
    print("   - Web界面: http://localhost:5000")
    print("   - API信息: http://localhost:5000/api/info")
    print("   - 连接测试: http://localhost:5000/test-connection")

if __name__ == '__main__':
    main()
