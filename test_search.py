#!/usr/bin/env python3
"""
测试搜索功能的演示脚本
"""

import requests
import json
import time

def test_local_search():
    """测试本地搜索功能"""
    print("=== 测试本地搜索功能 ===")
    
    # 测试数据
    test_cases = [
        {
            "name": "搜索earnings相关字段",
            "data": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "earnings",
                "category": "",
                "type": "",
                "limit": 10,
                "use_api": False
            }
        },
        {
            "name": "搜索analyst类别的MATRIX类型数据",
            "data": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "",
                "category": "analyst",
                "type": "MATRIX",
                "limit": 5,
                "use_api": False
            }
        },
        {
            "name": "搜索price相关的pv类别数据",
            "data": {
                "region": "USA",
                "delay": 1,
                "universe": "TOP3000",
                "search": "price",
                "category": "pv",
                "type": "",
                "limit": 5,
                "use_api": False
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 50)
        
        try:
            response = requests.post(
                'http://localhost:5000/search',
                json=test_case['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 成功找到 {result['count']} 个结果")
                    
                    # 显示前3个结果
                    for j, item in enumerate(result['results'][:3], 1):
                        print(f"  {j}. {item['id']}: {item['description'][:80]}...")
                else:
                    print(f"❌ 搜索失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(1)  # 避免请求过快

def test_api_connection():
    """测试API连接"""
    print("\n=== 测试API连接 ===")
    
    try:
        response = requests.get('http://localhost:5000/test_connection', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ API连接成功！用户ID: {result.get('user_id', 'N/A')}")
            else:
                print(f"❌ API连接失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")

def test_api_search():
    """测试API搜索功能（如果连接可用）"""
    print("\n=== 测试API搜索功能 ===")
    
    test_data = {
        "region": "USA",
        "delay": 1,
        "universe": "TOP3000",
        "search": "quarterly earnings",
        "category": "",
        "type": "MATRIX",
        "limit": 5,
        "use_api": True
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/search',
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ API搜索成功！找到 {result['count']} 个结果")
                
                # 显示前3个结果
                for i, item in enumerate(result['results'][:3], 1):
                    print(f"  {i}. {item.get('id', 'N/A')}: {item.get('description', 'N/A')[:80]}...")
            else:
                print(f"❌ API搜索失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求错误: {e}")

def main():
    print("数据字段检索器 - 功能测试")
    print("=" * 60)
    
    # 检查服务是否运行
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ 服务未正常运行，请先启动 app.py")
            return
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务，请确保 app.py 正在运行")
        return
    
    print("✅ 服务运行正常")
    
    # 执行测试
    test_local_search()
    test_api_connection()
    
    # 询问是否测试API搜索
    try:
        user_input = input("\n是否测试API搜索功能？(y/n): ").lower().strip()
        if user_input == 'y':
            test_api_search()
    except KeyboardInterrupt:
        print("\n测试中断")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
