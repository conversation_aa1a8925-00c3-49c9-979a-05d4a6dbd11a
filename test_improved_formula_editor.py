#!/usr/bin/env python3
"""
测试改进后的公式编辑器功能
"""

import itertools

def simulate_improved_formula_editor():
    """模拟改进后的公式编辑器工作流程"""
    print("📝 改进后的公式编辑器功能演示")
    print("=" * 60)
    
    # 模拟搜索结果
    mock_factors = [
        {'id': 'CLOSE_PRICE', 'description': '收盘价'},
        {'id': 'HIGH_PRICE', 'description': '最高价'},
        {'id': 'LOW_PRICE', 'description': '最低价'},
        {'id': 'VOLUME', 'description': '成交量'},
        {'id': 'TURNOVER', 'description': '成交额'},
        {'id': 'MARKET_CAP', 'description': '市值'},
        {'id': 'PE_RATIO', 'description': '市盈率'},
        {'id': 'PB_RATIO', 'description': '市净率'}
    ]
    
    print(f"🔍 模拟搜索结果: {len(mock_factors)} 个因子")
    for i, factor in enumerate(mock_factors, 1):
        print(f"   {i}. {factor['id']} - {factor['description']}")
    
    # 测试用例
    test_cases = [
        {
            'name': '简单比率公式',
            'template': '<price/> / <volume/>',
            'config': {
                'price': ['CLOSE_PRICE', 'HIGH_PRICE', 'LOW_PRICE'],
                'volume': ['VOLUME', 'TURNOVER']
            }
        },
        {
            'name': '技术分析指标',
            'template': '(<high/> + <low/> + <close/>) / 3',
            'config': {
                'high': ['HIGH_PRICE'],
                'low': ['LOW_PRICE'],
                'close': ['CLOSE_PRICE']
            }
        },
        {
            'name': '复杂财务比率',
            'template': 'log(<market_cap/>) / sqrt(<pe_ratio/>)',
            'config': {
                'market_cap': ['MARKET_CAP'],
                'pe_ratio': ['PE_RATIO', 'PB_RATIO']
            }
        },
        {
            'name': '多因子组合',
            'template': '(<factor1/> + <factor2/> - <factor3/>) / 3',
            'config': {
                'factor1': ['CLOSE_PRICE', 'HIGH_PRICE'],
                'factor2': ['VOLUME', 'TURNOVER'],
                'factor3': ['PE_RATIO', 'PB_RATIO']
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*20} 测试用例 {i}: {test_case['name']} {'='*20}")
        print(f"📋 模板: {test_case['template']}")
        
        # 解析占位符
        import re
        placeholders = re.findall(r'<([^/>]+)/>', test_case['template'])
        print(f"🔍 占位符: {placeholders}")
        
        # 显示配置
        print("⚙️ 占位符配置:")
        for placeholder in placeholders:
            factors = test_case['config'].get(placeholder, [])
            print(f"   <{placeholder}/>: {factors}")
        
        # 生成公式
        formulas = generate_formulas_with_config(
            test_case['template'], 
            placeholders, 
            test_case['config']
        )
        
        print(f"📊 生成结果: {len(formulas)} 个公式")
        print("前5个公式:")
        for j, formula in enumerate(formulas[:5], 1):
            print(f"   {j}. {formula}")
        
        if len(formulas) > 5:
            print(f"   ... (还有{len(formulas) - 5}个)")
        
        print(f"📤 导出格式预览:")
        export_preview = ',\n'.join(formulas[:3])
        if len(formulas) > 3:
            export_preview += ',\n...'
        print(f"```\n{export_preview}\n```")

def generate_formulas_with_config(template, placeholders, config):
    """根据配置生成公式"""
    # 获取每个占位符对应的因子列表
    placeholder_factors = []
    for placeholder in placeholders:
        factors = config.get(placeholder, [])
        if not factors:
            print(f"⚠️ 占位符 <{placeholder}/> 没有配置因子")
            return []
        placeholder_factors.append(factors)
    
    # 生成所有组合
    formulas = []
    for combination in itertools.product(*placeholder_factors):
        formula = template
        for placeholder, factor in zip(placeholders, combination):
            formula = formula.replace(f'<{placeholder}/>', factor)
        formulas.append(formula)
    
    return formulas

def demonstrate_advantages():
    """演示改进后功能的优势"""
    print("\n" + "="*60)
    print("🌟 改进后功能的优势")
    print("="*60)
    
    print("\n✅ 解决的问题:")
    print("   1. 多个占位符的精确控制")
    print("   2. 每个占位符可以独立配置因子")
    print("   3. 灵活的因子选择（不限于前N个）")
    print("   4. 直观的配置界面")
    print("   5. 精确的组合数量控制")
    
    print("\n🔧 新的工作流程:")
    print("   1. 输入公式模板: <price/> / <volume/>")
    print("   2. 点击'🔍 解析模板'")
    print("   3. 为 <price/> 选择: [CLOSE_PRICE, HIGH_PRICE]")
    print("   4. 为 <volume/> 选择: [VOLUME, TURNOVER]")
    print("   5. 生成 2×2=4 个公式:")
    print("      - CLOSE_PRICE / VOLUME")
    print("      - CLOSE_PRICE / TURNOVER")
    print("      - HIGH_PRICE / VOLUME")
    print("      - HIGH_PRICE / TURNOVER")
    
    print("\n💡 使用场景:")
    print("   📈 技术分析: 不同价格指标的组合")
    print("   💰 财务分析: 不同财务比率的配对")
    print("   📊 风险分析: 多种风险因子的组合")
    print("   🔍 因子挖掘: 探索不同因子的相互作用")
    
    print("\n🎯 用户体验:")
    print("   ✅ 精确控制每个占位符")
    print("   ✅ 可视化的因子选择界面")
    print("   ✅ 快速选择按钮（前3个、前5个等）")
    print("   ✅ 实时预览生成数量")
    print("   ✅ 防止生成过多公式的保护机制")

def show_complex_example():
    """展示复杂应用场景"""
    print("\n" + "="*60)
    print("🚀 复杂应用场景演示")
    print("="*60)
    
    # 复杂的多因子评分模型
    complex_template = "0.3 * <profitability/> + 0.4 * <growth/> + 0.3 * <valuation/>"
    
    complex_config = {
        'profitability': ['ROE', 'ROA', 'GROSS_MARGIN'],
        'growth': ['REVENUE_GROWTH', 'EARNINGS_GROWTH', 'BOOK_VALUE_GROWTH'],
        'valuation': ['PE_RATIO', 'PB_RATIO', 'PS_RATIO']
    }
    
    print(f"📋 复杂模板: {complex_template}")
    print("⚙️ 配置:")
    for key, values in complex_config.items():
        print(f"   <{key}/>: {values}")
    
    # 计算组合数
    total_combinations = 1
    for values in complex_config.values():
        total_combinations *= len(values)
    
    print(f"📊 将生成: {total_combinations} 个公式 (3×3×3=27)")
    
    # 生成几个示例
    formulas = generate_formulas_with_config(complex_template, 
                                           list(complex_config.keys()), 
                                           complex_config)
    
    print("📝 示例公式:")
    for i, formula in enumerate(formulas[:5], 1):
        print(f"   {i}. {formula}")
    
    print(f"\n💡 这种方式可以系统性地探索 {total_combinations} 种不同的多因子模型组合！")

def main():
    simulate_improved_formula_editor()
    demonstrate_advantages()
    show_complex_example()
    
    print("\n" + "="*60)
    print("🎉 改进后的公式编辑器功能完成！")
    print("="*60)
    
    print("\n🌟 核心改进:")
    print("   ✅ 每个占位符独立配置")
    print("   ✅ 灵活的因子选择")
    print("   ✅ 直观的用户界面")
    print("   ✅ 精确的组合控制")
    print("   ✅ 强大的批量生成能力")
    
    print("\n🚀 现在可以处理任意复杂的多占位符公式！")

if __name__ == "__main__":
    main()
