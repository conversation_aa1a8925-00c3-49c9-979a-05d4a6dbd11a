#!/usr/bin/env python3
"""
直接测试API搜索功能
"""

import wqb
import time
import ssl
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_direct():
    """直接测试API搜索"""
    print("🔍 直接测试WQB API搜索...")
    
    try:
        # 创建logger和session
        logger = wqb.wqb_logger()
        wqbs = wqb.WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)
        
        # 测试认证
        print("1. 测试API认证...")
        auth_resp = wqbs.auth_request()
        if auth_resp.ok:
            user_id = auth_resp.json()['user']['id']
            print(f"✅ 认证成功，用户ID: {user_id}")
        else:
            print(f"❌ 认证失败: {auth_resp.status_code}")
            return False
        
        # 测试简单搜索
        print("\n2. 测试简单API搜索...")
        start_time = time.time()
        
        try:
            resps = wqbs.search_fields(
                'USA',      # region
                1,          # delay
                'TOP3000',  # universe
                search='earnings',  # 简单搜索词
                category='pv',      # 限制类别
                type='MATRIX',      # 限制类型
                limit=5             # 只要5个结果
            )

            # 将生成器转换为列表
            resp_list = list(resps)

            end_time = time.time()
            duration = end_time - start_time

            print(f"⏱️  API响应时间: {duration:.2f} 秒")
            print(f"📊 返回响应数: {len(resp_list)}")

            total_results = 0
            for i, resp in enumerate(resp_list, 1):
                if resp.ok:
                    data = resp.json()
                    count = data.get('count', 0)
                    results = data.get('results', [])
                    total_results += len(results)
                    
                    print(f"   响应 {i}: {len(results)} 个结果 (总计: {count})")
                    
                    # 显示第一个结果
                    if results:
                        first = results[0]
                        print(f"   示例: {first.get('id', 'N/A')} - {first.get('description', 'N/A')[:50]}...")
                else:
                    print(f"   响应 {i}: HTTP {resp.status_code} 错误")
            
            print(f"\n✅ API搜索成功！总共获得 {total_results} 个结果")
            return True
            
        except ssl.SSLError as ssl_e:
            print(f"❌ SSL错误: {ssl_e}")
            print("💡 这可能是网络或SSL配置问题")
            return False
        except Exception as api_e:
            print(f"❌ API搜索错误: {api_e}")
            return False
            
    except Exception as e:
        print(f"❌ 初始化错误: {e}")
        return False

def test_different_searches():
    """测试不同的搜索参数"""
    print("\n🔄 测试不同搜索参数...")
    
    try:
        logger = wqb.wqb_logger()
        wqbs = wqb.WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)
        
        test_cases = [
            {
                'name': '搜索price相关字段',
                'params': {
                    'region': 'USA',
                    'delay': 1,
                    'universe': 'TOP3000',
                    'search': 'price',
                    'type': 'MATRIX',
                    'limit': 3
                }
            },
            {
                'name': '搜索analyst类别',
                'params': {
                    'region': 'USA',
                    'delay': 1,
                    'universe': 'TOP3000',
                    'category': 'analyst',
                    'limit': 3
                }
            },
            {
                'name': '搜索VECTOR类型',
                'params': {
                    'region': 'USA',
                    'delay': 1,
                    'universe': 'TOP3000',
                    'type': 'VECTOR',
                    'limit': 3
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print("-" * 40)
            
            try:
                start_time = time.time()
                resps = list(wqbs.search_fields(**test_case['params']))
                end_time = time.time()

                total_results = 0
                for resp in resps:
                    if resp.ok:
                        data = resp.json()
                        results = data.get('results', [])
                        total_results += len(results)
                
                print(f"✅ 成功！用时 {end_time - start_time:.2f}秒，获得 {total_results} 个结果")
                
            except Exception as e:
                print(f"❌ 失败: {e}")
            
            time.sleep(1)  # 避免请求过快
            
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")

def main():
    print("=" * 60)
    print("🧪 WQB API 直接测试")
    print("=" * 60)
    
    # 基本API测试
    basic_success = test_api_direct()
    
    if basic_success:
        # 如果基本测试成功，进行更多测试
        test_different_searches()
        
        print("\n" + "=" * 60)
        print("🎉 API测试总结:")
        print("✅ WQB API搜索功能正常工作")
        print("💡 建议:")
        print("   - API搜索可能偶尔遇到SSL连接问题")
        print("   - 这是网络层面的问题，不是代码问题")
        print("   - 可以在网页中重试API搜索")
        print("   - 或者使用本地搜索作为备选方案")
    else:
        print("\n" + "=" * 60)
        print("⚠️  API测试总结:")
        print("❌ API搜索遇到连接问题")
        print("💡 建议:")
        print("   - 检查网络连接")
        print("   - 使用本地搜索功能")
        print("   - 稍后重试API搜索")

if __name__ == "__main__":
    main()
