# 📝 公式编辑器使用指南

## 🌟 功能概述

公式编辑器是一个强大的工具，允许您：
- 📝 编写包含占位符的公式模板
- 🔍 使用搜索结果中的因子自动填充占位符
- 🔢 选择"前N个"因子进行批量生成
- 📊 生成多个公式并以逗号分隔格式导出

## 🚀 快速开始

### 1. 准备工作
1. **登录账户**: 点击"🔑 API登录"输入WQB账户信息
2. **搜索因子**: 设置搜索条件并点击"🔍 搜索"获取因子数据
3. **打开编辑器**: 点击"📝 公式编辑器"按钮

### 2. 编写公式模板
在公式模板中使用 `<占位符名称/>` 标记需要填入因子的位置：

```
示例模板:
- 简单比率: <price_factor/> / <volume_factor/>
- 数学运算: log(<market_cap/>) * sqrt(<pe_ratio/>)
- 多因子组合: (<factor1/> + <factor2/> + <factor3/>) / 3
- 技术指标: (<high/> + <low/> + <close/>) / 3
```

### 3. 配置生成选项
- **前N个因子**: 从搜索结果中选择前N个因子（1-100）
- **填充模式**: 
  - **顺序填充**: 每个公式使用连续的因子
  - **所有组合**: 生成所有可能的因子组合

### 4. 生成和导出
- **👁️ 预览**: 查看将要生成的公式概览
- **🚀 生成公式**: 批量生成所有公式
- **📋 复制公式**: 一键复制到剪贴板
- **💾 导出文件**: 保存为文本文件

## 📋 占位符语法

### 基本语法
```
<占位符名称/>
```

### 命名规则
- 使用有意义的名称，如 `<price/>`, `<volume/>`, `<market_cap/>`
- 支持下划线，如 `<close_price/>`, `<pe_ratio/>`
- 区分大小写
- 不能包含空格或特殊字符

### 示例模板
```html
<!-- 简单比率 -->
<numerator/> / <denominator/>

<!-- 技术分析 -->
(<high/> + <low/> + <close/>) / 3

<!-- 财务比率 -->
<revenue/> / <total_assets/>

<!-- 复合指标 -->
log(<market_cap/>) * (<roe/> + <roa/>) / 2

<!-- 波动率计算 -->
sqrt((<high/> - <low/>) / <close/>)
```

## 🔄 填充模式详解

### 顺序填充模式
每个公式使用连续的因子，适合生成相似结构的公式。

**示例**:
- 模板: `<factor1/> / <factor2/>`
- 因子: [A, B, C, D, E]
- 生成结果:
  ```
  A / B
  B / C  
  C / D
  D / E
  E / A
  ```

### 所有组合模式
生成所有可能的因子组合，适合探索不同的因子配对。

**示例**:
- 模板: `<factor1/> - <factor2/>`
- 因子: [A, B, C]
- 生成结果:
  ```
  A - B
  A - C
  B - A
  B - C
  C - A
  C - B
  ```

## 📊 实际应用场景

### 1. 技术分析指标
```html
模板: (<high/> + <low/> + <close/>) / 3
用途: 生成不同股票的典型价格指标
```

### 2. 财务比率分析
```html
模板: <revenue/> / <total_assets/>
用途: 批量生成资产周转率公式
```

### 3. 相对强度指标
```html
模板: <stock_return/> / <market_return/>
用途: 计算股票相对市场的表现
```

### 4. 波动率指标
```html
模板: sqrt(<variance/>) / <mean/>
用途: 生成变异系数公式
```

### 5. 复合评分模型
```html
模板: 0.3 * <profitability/> + 0.4 * <growth/> + 0.3 * <valuation/>
用途: 创建多因子评分模型
```

## 💡 最佳实践

### 1. 模板设计
- **语义清晰**: 使用有意义的占位符名称
- **结构合理**: 确保数学表达式正确
- **避免除零**: 考虑分母可能为零的情况

### 2. 因子选择
- **相关性**: 选择与分析目标相关的因子
- **数据质量**: 优先选择数据完整性好的因子
- **数量平衡**: 根据需要选择合适的因子数量

### 3. 结果验证
- **预览检查**: 生成前先预览确认逻辑正确
- **抽样验证**: 检查几个生成的公式是否符合预期
- **格式确认**: 确保导出格式满足使用需求

## 🔧 高级技巧

### 1. 复杂数学函数
```html
<!-- 对数变换 -->
log(<market_cap/>) / log(<revenue/>)

<!-- 平方根标准化 -->
<value/> / sqrt(<variance/>)

<!-- 指数平滑 -->
exp(<growth_rate/>) * <base_value/>
```

### 2. 条件表达式（需要目标系统支持）
```html
<!-- 三元运算符风格 -->
<condition/> ? <true_value/> : <false_value/>

<!-- 最大最小值 -->
max(<value1/>, <value2/>)
min(<value1/>, <value2/>)
```

### 3. 批量处理策略
- **分批生成**: 对于大量因子，分批处理避免系统负载过重
- **模板复用**: 保存常用模板以便重复使用
- **结果管理**: 为不同的分析目标创建不同的公式集合

## 📤 导出格式

### 逗号分隔格式
```
CLOSE_PRICE / VOLUME,
log(MARKET_CAP) * sqrt(PE_RATIO),
(FACTOR1 + FACTOR2 + FACTOR3) / 3,
HIGH_PRICE - LOW_PRICE
```

### 使用场景
- **Excel导入**: 可以直接粘贴到Excel中
- **编程使用**: 适合在Python、R等环境中使用
- **数据库查询**: 可以作为SQL表达式的一部分
- **其他系统**: 大多数系统都支持逗号分隔格式

## 🎯 总结

公式编辑器提供了一个高效的方式来：
- ✅ 批量生成相似结构的公式
- ✅ 自动化因子替换过程
- ✅ 探索不同的因子组合
- ✅ 标准化公式格式
- ✅ 提高分析效率

通过合理使用占位符和填充模式，您可以快速生成大量高质量的分析公式，大大提升量化分析的效率！🚀
