#!/bin/bash

echo "============================================================"
echo "🔍 数据字段检索器 - Linux/Mac启动脚本"
echo "============================================================"

echo "🚀 正在启动..."
python3 start.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 启动失败，尝试修复兼容性问题..."
    echo "🔧 使用修复脚本..."
    python3 fix_and_start.py

    if [ $? -ne 0 ]; then
        echo ""
        echo "❌ 修复失败，尝试最基本的启动..."
        echo "📦 安装基础依赖..."
        pip3 install flask pandas wqb requests urllib3
        echo "🚀 启动应用（无翻译功能）..."
        python3 app.py
    fi
fi
