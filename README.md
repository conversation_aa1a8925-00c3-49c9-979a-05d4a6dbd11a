# 数据字段检索器

这是一个基于Flask的Web应用程序，用于搜索和浏览WQB数据字段。它支持本地CSV数据搜索和API实时搜索两种模式。

## 功能特性

- **多维度筛选**: 支持按Region、Delay、Universe、Category、Type等维度筛选数据
- **关键词搜索**: 在字段描述中进行全文搜索
- **双模式搜索**: 
  - 本地模式：从CSV文件中快速搜索
  - API模式：通过WQB API实时搜索
- **响应式界面**: 适配桌面和移动设备
- **实时结果**: 显示搜索结果的详细信息，包括覆盖率、用户数等

## 系统要求

- Python 3.7+
- Flask
- pandas
- wqb (WQB Python SDK)

## 安装和运行

1. 确保已安装所需依赖：
```bash
pip install flask pandas
```

2. 启动应用：
```bash
python app.py
```

3. 在浏览器中访问：
```
http://localhost:5000
```

## 使用说明

### 搜索参数

- **Region**: 数据区域选择
  - USA: 美国
  - EUR: 欧洲
  - CHN: 中国
  - ASI: 亚洲
  - GLB: 全球

- **Delay**: 数据延迟
  - 0: 实时数据
  - 1: 延迟数据

- **Universe**: 数据集范围
  - TOP3000, TOP1000, TOP500, TOP200等

- **搜索关键词**: 在字段描述中搜索的关键词

- **Category**: 数据大类
  - analyst: 分析师数据
  - fundamental: 基本面数据
  - model: 模型数据
  - pv: 价格成交量数据
  - risk: 风险数据
  - 等等...

- **Type**: 数据类型
  - MATRIX: 矩阵数据
  - VECTOR: 向量数据
  - GROUP: 分组数据
  - UNIVERSE: 股票池数据
  - SYMBOL: 符号数据

- **最大结果数**: 限制返回的结果数量（1-1000）

- **使用API搜索**: 勾选后使用WQB API实时搜索，否则使用本地CSV数据

### 搜索模式

#### 本地模式（推荐）
- 从`data/split_files/`文件夹中的CSV文件搜索
- 速度快，无需网络连接
- 支持所有筛选条件

#### API模式
- 通过WQB API实时搜索
- 数据最新，但需要网络连接
- 可能受到API限制

## 数据文件结构

本地CSV文件应包含以下字段：
- `id`: 字段ID
- `description`: 字段描述
- `region`: 区域
- `delay`: 延迟
- `universe`: 数据集
- `type`: 数据类型
- `coverage`: 覆盖率
- `userCount`: 用户数
- `alphaCount`: Alpha数
- `category.id`: 类别ID
- `category.name`: 类别名称
- `dataset.name`: 数据集名称

## 文件说明

- `app.py`: Flask主应用文件
- `templates/index.html`: 网页模板
- `static/css/style.css`: 样式文件
- `static/js/app.js`: JavaScript交互逻辑
- `data/split_files/`: CSV数据文件目录
- `extract_categories.py`: 提取类别信息的工具脚本
- `extract_options.py`: 提取选项信息的工具脚本

## API端点

- `GET /`: 主页面
- `POST /search`: 执行搜索
- `GET /test_connection`: 测试API连接

## 注意事项

1. 确保`data/split_files/`目录包含相应的CSV文件
2. API模式需要有效的WQB账户凭据
3. 大量数据搜索时建议使用本地模式
4. 移动设备上界面会自动适配

## 故障排除

1. **无法启动应用**：检查Python版本和依赖是否正确安装
2. **搜索无结果**：确认CSV文件存在且格式正确
3. **API连接失败**：检查网络连接和WQB凭据
4. **页面显示异常**：清除浏览器缓存并刷新页面
