# 🔍 数据字段检索器

一个功能强大的Web应用，用于搜索和检索金融数据字段信息，支持中文搜索、公式编辑和选择性翻译。

## ✨ 核心功能

### 🔍 智能搜索
- **多条件搜索**: 支持region、delay、universe、category、type等多维度搜索
- **中文搜索**: 自动识别中文并翻译为英文进行搜索
- **实时API**: 连接WQB API获取最新数据
- **本地缓存**: 支持本地数据文件搜索

### 📝 公式编辑器
- **模板编辑**: 使用占位符创建公式模板，如 `<price/> / <volume/>`
- **智能赋值**: 搜索结果可直接赋值给占位符
- **批量生成**: 自动生成所有可能的公式组合
- **占位符记忆**: 自动记住使用过的占位符

### 🌐 选择性翻译
- **单条翻译**: 每个搜索结果都有独立的翻译按钮
- **双语对照**: 同时显示中文翻译和英文原文
- **即时翻译**: 点击即翻译，响应快速
- **状态切换**: 在原文和译文之间自由切换

### 🔐 安全登录
- **账户认证**: 安全的WQB账户登录系统
- **会话管理**: 智能的登录状态管理
- **隐私保护**: 账户信息不会被存储

## 🚀 快速开始

### 🎯 一键启动（推荐）
```bash
# 自动检查依赖并启动应用
python start.py
```

### 🖥️ 平台特定启动
**Windows用户**:
```cmd
start.bat
```

**Linux/Mac用户**:
```bash
./start.sh
```

### ⚙️ 手动启动（可选）
如果需要手动控制：

1. **安装依赖**:
```bash
pip install -r requirements.txt
```

2. **配置账户**（可选）:
```bash
cp config_example.py config.py
# 编辑config.py填入WQB账户信息
```

3. **启动应用**:
```bash
python app.py
```

### 🌐 访问应用
启动后在浏览器中打开：http://127.0.0.1:5000

## 📖 使用指南

### 基础搜索
1. 在搜索框输入关键词（支持中英文）
2. 选择搜索条件（region、universe等）
3. 点击"🔍 搜索"按钮
4. 查看搜索结果

### 公式编辑
1. 点击"📝 公式编辑器"按钮
2. 输入公式模板：`<price/> / <volume/>`
3. 回到搜索页面，搜索相关因子
4. 使用"快速赋值"功能为占位符赋值
5. 返回公式编辑器，点击"🚀 生成公式"

### 选择性翻译
1. 进行搜索获得结果
2. 点击感兴趣结果的"🔤 翻译"按钮
3. 查看中英文对照的描述
4. 使用"📄 原文"按钮切换显示

## 📁 项目结构

```
数据字段检索器/
├── app.py                          # 主应用文件
├── config_example.py               # 配置文件模板
├── 12.py                          # API测试脚本
├── templates/
│   └── index.html                 # 前端界面
├── data/
│   └── split_files/               # 本地数据文件
├── static/                        # 静态资源（如需要）
├── README.md                      # 项目说明
├── .gitignore                     # Git忽略文件
└── 文档/
    ├── FINAL_FORMULA_EDITOR_GUIDE.md      # 公式编辑器指南
    ├── CHINESE_SUPPORT_GUIDE.md           # 中文支持指南
    └── SELECTIVE_TRANSLATION_GUIDE.md     # 选择性翻译指南
```

## 🔧 技术栈

- **后端**: Flask + Python
- **前端**: Bootstrap 5 + JavaScript
- **API**: WQB API
- **翻译**: Google Translate
- **数据**: Pandas + CSV

## 🛡️ 安全说明

- 账户信息通过配置文件管理，不会硬编码在代码中
- `config.py` 文件已添加到 `.gitignore`，不会被提交到版本控制
- 登录信息仅用于API认证，不会被存储或记录

## 📚 详细文档

- [公式编辑器完整指南](FINAL_FORMULA_EDITOR_GUIDE.md)
- [中文支持功能说明](CHINESE_SUPPORT_GUIDE.md)
- [选择性翻译使用指南](SELECTIVE_TRANSLATION_GUIDE.md)

## 🎯 使用场景

### 金融研究
- 搜索特定的金融指标和比率
- 创建自定义的计算公式
- 理解复杂的金融术语

### 数据分析
- 快速找到所需的数据字段
- 批量生成计算公式
- 验证数据字段的含义

### 学习研究
- 中英文对照学习金融术语
- 理解数据字段的具体含义
- 探索不同市场的数据结构

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和研究使用。
