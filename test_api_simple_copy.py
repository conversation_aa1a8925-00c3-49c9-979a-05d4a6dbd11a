#!/usr/bin/env python3
"""
直接复制12.py的逻辑测试API
"""

import wqb
import json

def test_api_like_12py():
    """完全按照12.py的方式测试API"""
    print("🔍 按照12.py的方式测试API...")
    
    # 完全复制12.py的初始化
    logger = wqb.wqb_logger()
    wqbs = wqb.WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)
    
    # 完全复制12.py的参数
    region = 'USA'
    delay = 1
    universe = 'TOP3000'
    
    print(f"参数: region={region}, delay={delay}, universe={universe}")
    
    try:
        # 完全按照12.py的方式调用
        resps = wqbs.search_fields(
            region,
            delay,
            universe,
            search='quarterly earnings',
            category='pv',
            theme=False,  # 重要！
            type='MATRIX',
            limit=5  # 只要5个结果测试
        )
        
        print("开始处理响应...")
        
        total_results = 0
        for idx, resp in enumerate(resps, start=1):
            print(f"处理响应 {idx}...")
            if resp.ok:
                data = resp.json()
                print(f"响应 {idx} 成功: {data.get('count', 0)} 个结果")
                
                results = data.get('results', [])
                total_results += len(results)
                
                # 显示第一个结果
                if results:
                    first = results[0]
                    print(f"  示例: {first.get('id', 'N/A')}")
                    print(f"  描述: {first.get('description', 'N/A')[:60]}...")
                    print(f"  类别: {first.get('category', {}).get('name', 'N/A')}")
            else:
                print(f"响应 {idx} 失败: HTTP {resp.status_code}")
        
        print(f"\n✅ 成功！总共获得 {total_results} 个结果")
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_flask_api_call():
    """测试Flask应用的API调用"""
    print("\n🌐 测试Flask应用的API调用...")
    
    import requests
    
    test_data = {
        'region': 'USA',
        'delay': 1,
        'universe': 'TOP3000',
        'search': 'quarterly earnings',
        'category': 'pv',
        'type': 'MATRIX',
        'limit': 5,
        'use_api': True
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/search', 
            json=test_data, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Flask API调用成功！找到 {result.get('count', 0)} 个结果")
                
                results = result.get('results', [])
                if results:
                    first = results[0]
                    print(f"  示例: {first.get('id', 'N/A')}")
                    print(f"  描述: {first.get('description', 'N/A')[:60]}...")
                return True
            else:
                print(f"❌ Flask API调用失败: {result.get('error')}")
                return False
        else:
            print(f"❌ Flask HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Flask请求错误: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 API搜索对比测试")
    print("=" * 60)
    
    # 测试直接API调用（像12.py一样）
    direct_success = test_api_like_12py()
    
    # 测试Flask应用的API调用
    flask_success = test_flask_api_call()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   直接API调用: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"   Flask API调用: {'✅ 成功' if flask_success else '❌ 失败'}")
    
    if direct_success and flask_success:
        print("\n🎉 两种方式都成功！API搜索功能正常。")
    elif direct_success:
        print("\n⚠️  直接调用成功，Flask调用失败。检查Flask应用逻辑。")
    else:
        print("\n❌ 直接API调用失败，可能是网络或认证问题。")

if __name__ == "__main__":
    main()
