import wqb
from wqb import WQBSession, print

# 尝试导入配置文件
try:
    from config import WQB_USERNAME, WQB_PASSWORD
    print("✅ 成功加载配置文件")
except ImportError:
    print("❌ 未找到配置文件 config.py")
    print("请复制 config_example.py 为 config.py 并填入您的账户信息")
    WQB_USERNAME = input("请输入WQB用户名/邮箱: ").strip()
    WQB_PASSWORD = input("请输入WQB密码: ").strip()

# Create `logger`
logger = wqb.wqb_logger()
wqb.print(f"{logger.name = }")  # print(f"{logger.name = }", flush=True)

# Create `wqbs`
wqbs = WQBSession((WQB_USERNAME, WQB_PASSWORD), logger=logger)

# Test connectivity (Optional)
resp = wqbs.auth_request()
print(resp.status_code)           # 201
print(resp.ok)                    # True
print(resp.json()['user']['id'])  # <Your BRAIN User ID>

# from wqb import FilterRange  # 如果需要使用FilterRange，请取消注释

region = 'USA'  # 'USA'
delay = 1  # 1, 0
universe = 'TOP3000'  # 'TOP3000'
resps = wqbs.search_fields(
    region,
    delay,
    universe,
    # dataset_id='<dataset_id>',  # 'pv1'
    search='quarterly earnings',  # 'open'
    category='pv',  # 'pv', 'model', 'analyst','fundamental'
    theme=False,  # True, False
    # coverage=FilterRange.from_str('[0.8, inf)'),
    type='MATRIX',  # 'MATRIX', 'VECTOR', 'GROUP', 'UNIVERSE'
    # alpha_count=FilterRange.from_str('[100, 200)'),
    # user_count=FilterRange.from_str('[1, 99]'),
    # order='<order>',  # 'coverage', '-coverage', 'alphaCount', '-alphaCount'
    limit=1000,
    # offset=0,
    # others=[],  # ['other_param_0=xxx', 'other_param_1=yyy']
)
for idx, resp in enumerate(resps, start=1):
    print(idx)
    print(resp.json())