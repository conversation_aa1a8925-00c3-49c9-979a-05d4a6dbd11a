import wqb
from typing import List, Dict, Any, Optional

# Create `logger`
logger = wqb.wqb_logger()
wqb.print(f"{logger.name = }")  # print(f"{logger.name = }", flush=True)

from wqb import WQBSession, print, FilterRange

class WQBDataSearcher:
    """WQB数据搜索器类"""

    def __init__(self, email: str, password: str):
        """初始化WQB会话"""
        self.wqbs = WQBSession((email, password), logger=logger)
        self._test_connection()

    def _test_connection(self):
        """测试连接"""
        resp = self.wqbs.auth_request()
        print(f"连接状态: {resp.status_code}")
        print(f"连接成功: {resp.ok}")
        if resp.ok:
            print(f"用户ID: {resp.json()['user']['id']}")
        else:
            raise Exception("WQB连接失败")

    def search_fields(self,
                     region: str = 'USA',
                     delay: int = 1,
                     universe: str = 'TOP3000',
                     search: str = '',
                     category: str = '',
                     data_type: str = '',
                     limit: int = 1000,
                     theme: bool = False,
                     coverage_filter: Optional[str] = None,
                     alpha_count_filter: Optional[str] = None,
                     user_count_filter: Optional[str] = None,
                     order: Optional[str] = None,
                     offset: int = 0) -> List[Dict[str, Any]]:
        """
        搜索数据字段

        Args:
            region: 地区 ('USA', 'EUR', 'ASI', 'CHN', 'GLB')
            delay: 延迟 (0, 1)
            universe: 股票池 ('TOP3000', 'TOP1000', etc.)
            search: 搜索关键词
            category: 数据类别 ('pv', 'model', 'analyst', 'fundamental', etc.)
            data_type: 数据类型 ('MATRIX', 'VECTOR', 'GROUP', 'UNIVERSE')
            limit: 最大结果数
            theme: 是否包含主题
            coverage_filter: 覆盖率过滤器，如 '[0.8, inf)'
            alpha_count_filter: Alpha数量过滤器，如 '[100, 200)'
            user_count_filter: 用户数量过滤器，如 '[1, 99]'
            order: 排序方式 ('coverage', '-coverage', 'alphaCount', '-alphaCount')
            offset: 偏移量

        Returns:
            搜索结果列表
        """

        # 构建搜索参数
        kwargs = {
            'theme': theme,
            'limit': limit,
            'offset': offset
        }

        # 添加可选参数
        if search:
            kwargs['search'] = search
        if category:
            kwargs['category'] = category
        if data_type:
            kwargs['type'] = data_type
        if coverage_filter:
            kwargs['coverage'] = FilterRange.from_str(coverage_filter)
        if alpha_count_filter:
            kwargs['alpha_count'] = FilterRange.from_str(alpha_count_filter)
        if user_count_filter:
            kwargs['user_count'] = FilterRange.from_str(user_count_filter)
        if order:
            kwargs['order'] = order

        print(f"搜索参数: region={region}, delay={delay}, universe={universe}")
        print(f"其他参数: {kwargs}")

        # 执行搜索
        resps = self.wqbs.search_fields(region, delay, universe, **kwargs)

        # 收集结果
        results = []
        for idx, resp in enumerate(resps, start=1):
            if resp.ok:
                data = resp.json()
                results.extend(data.get('data', []))
                print(f"批次 {idx}: 获得 {len(data.get('data', []))} 条结果")
            else:
                print(f"批次 {idx} 请求失败: {resp.status_code}")

        print(f"总共获得 {len(results)} 条结果")
        return results

# 创建搜索器实例
searcher = WQBDataSearcher('<EMAIL>', 'Kyz417442')

# 示例搜索
if __name__ == "__main__":
    print("开始测试WQB搜索功能...")

    # 测试1: 简单搜索，不带过滤条件
    print("\n=== 测试1: 基础搜索 ===")
    try:
        results = searcher.search_fields(
            region='USA',
            delay=1,
            universe='TOP3000',
            search='',  # 不指定搜索词
            category='',  # 不指定类别
            data_type='',  # 不指定类型
            limit=10
        )
        print(f"基础搜索结果数量: {len(results)}")

        if results:
            for i, result in enumerate(results[:3], 1):
                print(f"\n结果 {i}:")
                print(f"  ID: {result.get('id', 'N/A')}")
                print(f"  描述: {result.get('description', 'N/A')[:80]}...")
                print(f"  类型: {result.get('type', 'N/A')}")
                print(f"  类别: {result.get('category', {}).get('name', 'N/A')}")
        else:
            print("没有找到结果")
    except Exception as e:
        print(f"基础搜索失败: {e}")

    # 测试2: 带关键词搜索
    print("\n=== 测试2: 关键词搜索 ===")
    try:
        results = searcher.search_fields(
            region='USA',
            delay=1,
            universe='TOP3000',
            search='price',  # 搜索价格相关
            limit=5
        )
        print(f"关键词搜索结果数量: {len(results)}")

        if results:
            for i, result in enumerate(results[:2], 1):
                print(f"\n结果 {i}:")
                print(f"  ID: {result.get('id', 'N/A')}")
                print(f"  描述: {result.get('description', 'N/A')[:80]}...")
        else:
            print("没有找到结果")
    except Exception as e:
        print(f"关键词搜索失败: {e}")

    # 测试3: 分类搜索
    print("\n=== 测试3: 分类搜索 ===")
    try:
        results = searcher.search_fields(
            region='USA',
            delay=1,
            universe='TOP3000',
            category='pv',  # 价格成交量数据
            limit=5
        )
        print(f"分类搜索结果数量: {len(results)}")

        if results:
            for i, result in enumerate(results[:2], 1):
                print(f"\n结果 {i}:")
                print(f"  ID: {result.get('id', 'N/A')}")
                print(f"  描述: {result.get('description', 'N/A')[:80]}...")
        else:
            print("没有找到结果")
    except Exception as e:
        print(f"分类搜索失败: {e}")

    print("\n测试完成！")