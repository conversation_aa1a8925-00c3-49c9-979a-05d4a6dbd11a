import wqb

# Create `logger`
logger = wqb.wqb_logger()
wqb.print(f"{logger.name = }")  # print(f"{logger.name = }", flush=True)

# Manual logging
# logger.info('This is an info for testing.')
# logger.warning('This is a warning for testing.')
from wqb import WQBSession, print

# Create `wqbs`
wqbs = WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)
# If `logger` was not created, use the following line instead.
# wqbs = WQBSession(('<email>', '<password>'))

# Test connectivity (Optional)
resp = wqbs.auth_request()
print(resp.status_code)           # 201
print(resp.ok)                    # True
print(resp.json()['user']['id'])  # <Your BRAIN User ID>

from wqb import FilterRange

# 尝试不同的搜索参数
test_cases = [
    {
        'name': '基础搜索 - 无过滤',
        'params': {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'limit': 5
        }
    },
    {
        'name': '搜索 - open',
        'params': {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'open',
            'limit': 5
        }
    },
    {
        'name': '搜索 - price',
        'params': {
            'region': 'USA',
            'delay': 1,
            'universe': 'TOP3000',
            'search': 'price',
            'limit': 5
        }
    }
]

for test_case in test_cases:
    print(f"\n{'='*50}")
    print(f"测试: {test_case['name']}")
    print(f"参数: {test_case['params']}")
    print('='*50)

    resps = wqbs.search_fields(**test_case['params'])

    found_data = False
    for idx, resp in enumerate(resps, start=1):
        print(f"批次 {idx}")
        if resp.ok:
            data = resp.json()
            data_count = len(data.get('data', []))
            print(f"状态: 成功, 数据条数: {data_count}")

            if data_count > 0:
                found_data = True
                for i, item in enumerate(data.get('data', [])[:2], 1):  # 只显示前2条
                    print(f"  {i}. ID: {item.get('id')}")
                    print(f"     描述: {item.get('description', '')[:60]}...")
                    print(f"     类型: {item.get('type')}")
                break  # 找到数据就停止
        else:
            print(f"状态: 失败 ({resp.status_code})")
            print(f"错误: {resp.text}")

        if idx >= 3:  # 最多尝试3个批次
            break

    if not found_data:
        print("未找到数据")
    print("-" * 50)
for idx, resp in enumerate(resps, start=1):
    print(f"批次 {idx}")
    if resp.ok:
        data = resp.json()
        print(f"状态: 成功, 数据条数: {len(data.get('data', []))}")
        for i, item in enumerate(data.get('data', [])[:3], 1):  # 只显示前3条
            print(f"  {i}. ID: {item.get('id')}")
            print(f"     描述: {item.get('description', '')[:80]}...")
    else:
        print(f"状态: 失败 ({resp.status_code})")
        print(f"错误: {resp.text}")
    print("-" * 50)