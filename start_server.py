#!/usr/bin/env python3
"""
数据字段检索器启动脚本
自动检测环境并启动合适的服务器
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖包"""
    required_packages = ['flask', 'pandas']
    optional_packages = ['wqb']
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        try:
            __import__(package)
        except ImportError:
            missing_optional.append(package)
    
    return missing_required, missing_optional

def install_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"正在安装缺失的包: {', '.join(packages)}")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        return True
    except subprocess.CalledProcessError:
        return False

def check_wqb_connection():
    """检查WQB连接"""
    try:
        import wqb
        from wqb import WQBSession
        
        # 尝试创建会话（不实际连接）
        return True
    except ImportError:
        return False
    except Exception:
        return False

def check_local_data():
    """检查本地数据"""
    data_dir = "data/split_files"
    if not os.path.exists(data_dir):
        return False
    
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    return len(csv_files) > 0

def main():
    print("=" * 60)
    print("🔍 数据字段检索器启动程序")
    print("=" * 60)
    
    # 检查依赖
    print("\n📦 检查依赖包...")
    missing_required, missing_optional = check_dependencies()
    
    if missing_required:
        print(f"❌ 缺少必需的包: {', '.join(missing_required)}")
        print("正在尝试自动安装...")
        if install_packages(missing_required):
            print("✅ 必需包安装成功")
        else:
            print("❌ 包安装失败，请手动安装:")
            print(f"pip install {' '.join(missing_required)}")
            return
    else:
        print("✅ 所有必需包已安装")
    
    # 检查WQB
    print("\n🌐 检查WQB连接...")
    wqb_available = check_wqb_connection()
    if wqb_available:
        print("✅ WQB包可用")
    else:
        print("⚠️  WQB包不可用")
        if 'wqb' in missing_optional:
            print("提示: 可以运行 'pip install wqb' 来安装WQB包")
    
    # 检查本地数据
    print("\n📁 检查本地数据...")
    local_data_available = check_local_data()
    if local_data_available:
        print("✅ 本地数据文件可用")
    else:
        print("⚠️  本地数据文件不可用")
    
    # 决定启动哪个服务器
    print("\n🚀 选择服务器模式...")
    
    if wqb_available:
        print("推荐使用WQB API模式（实时数据）")
        choice = input("选择模式 [1] WQB API模式 [2] 本地数据模式 [默认: 1]: ").strip()
        
        if choice == '2' and local_data_available:
            server_script = 'web_server.py'
            server_name = '本地数据服务器'
        elif choice == '2' and not local_data_available:
            print("❌ 本地数据不可用，切换到WQB API模式")
            server_script = 'wqb_web_server.py'
            server_name = 'WQB API服务器'
        else:
            server_script = 'wqb_web_server.py'
            server_name = 'WQB API服务器'
    
    elif local_data_available:
        print("使用本地数据模式")
        server_script = 'web_server.py'
        server_name = '本地数据服务器'
    
    else:
        print("❌ 既没有WQB包也没有本地数据，无法启动服务器")
        print("\n解决方案:")
        print("1. 安装WQB包: pip install wqb")
        print("2. 或者确保data/split_files/目录下有CSV数据文件")
        return
    
    # 启动服务器
    print(f"\n🎯 启动{server_name}...")
    print(f"执行: python {server_script}")
    print("\n" + "=" * 60)
    print("服务器启动后，请访问: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        # 给用户一点时间阅读信息
        time.sleep(2)
        
        # 启动服务器
        subprocess.run([sys.executable, server_script])
    
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except FileNotFoundError:
        print(f"\n❌ 找不到服务器脚本: {server_script}")
    except Exception as e:
        print(f"\n❌ 启动服务器时出错: {e}")

if __name__ == '__main__':
    main()
