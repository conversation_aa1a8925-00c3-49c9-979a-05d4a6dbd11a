<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速选择功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>🧪 快速选择功能测试</h2>
        
        <div class="card">
            <div class="card-header">
                <strong>&lt;test_placeholder/&gt;</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">选择因子</label>
                        <select multiple class="form-control" id="factors_0" style="height: 200px;">
                            <option value="0">CLOSE_PRICE - 收盘价</option>
                            <option value="1">HIGH_PRICE - 最高价</option>
                            <option value="2">LOW_PRICE - 最低价</option>
                            <option value="3">VOLUME - 成交量</option>
                            <option value="4">TURNOVER - 成交额</option>
                            <option value="5">MARKET_CAP - 市值</option>
                            <option value="6">PE_RATIO - 市盈率</option>
                            <option value="7">PB_RATIO - 市净率</option>
                        </select>
                        <small class="text-muted">按住Ctrl/Cmd多选因子</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">快速选择</label>
                        <div class="btn-group-vertical d-grid gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectTopN('0', 3)">前3个</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectTopN('0', 5)">前5个</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectTopN('0', 10)">前10个</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectAll('0')">全选</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection('0')">清空</button>
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" class="btn btn-info" onclick="showSelected()">显示已选择</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <div id="selectedInfo" class="alert alert-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 快速选择函数
        function selectTopN(placeholderIndex, n) {
            console.log(`调用 selectTopN(${placeholderIndex}, ${n})`);
            const select = document.getElementById(`factors_${placeholderIndex}`);
            if (!select) {
                console.error(`找不到选择框: factors_${placeholderIndex}`);
                alert(`错误：找不到选择框 factors_${placeholderIndex}`);
                return;
            }
            
            // 清空当前选择
            for (let option of select.options) {
                option.selected = false;
            }
            
            // 选择前n个
            for (let i = 0; i < Math.min(n, select.options.length); i++) {
                select.options[i].selected = true;
            }
            
            console.log(`已选择前${n}个因子`);
            showSelected();
        }

        function selectAll(placeholderIndex) {
            console.log(`调用 selectAll(${placeholderIndex})`);
            const select = document.getElementById(`factors_${placeholderIndex}`);
            if (!select) {
                console.error(`找不到选择框: factors_${placeholderIndex}`);
                alert(`错误：找不到选择框 factors_${placeholderIndex}`);
                return;
            }
            
            for (let option of select.options) {
                option.selected = true;
            }
            
            console.log(`已全选所有因子`);
            showSelected();
        }

        function clearSelection(placeholderIndex) {
            console.log(`调用 clearSelection(${placeholderIndex})`);
            const select = document.getElementById(`factors_${placeholderIndex}`);
            if (!select) {
                console.error(`找不到选择框: factors_${placeholderIndex}`);
                alert(`错误：找不到选择框 factors_${placeholderIndex}`);
                return;
            }
            
            for (let option of select.options) {
                option.selected = false;
            }
            
            console.log(`已清空选择`);
            showSelected();
        }

        function showSelected() {
            const select = document.getElementById('factors_0');
            const selectedOptions = Array.from(select.selectedOptions);
            const selectedTexts = selectedOptions.map(option => option.text);
            
            const info = document.getElementById('selectedInfo');
            if (selectedTexts.length > 0) {
                info.innerHTML = `<strong>已选择 ${selectedTexts.length} 个因子:</strong><br>${selectedTexts.join('<br>')}`;
                info.style.display = 'block';
            } else {
                info.innerHTML = '<strong>没有选择任何因子</strong>';
                info.style.display = 'block';
            }
        }

        // 页面加载完成后显示初始状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，测试快速选择功能');
            showSelected();
        });
    </script>
</body>
</html>
